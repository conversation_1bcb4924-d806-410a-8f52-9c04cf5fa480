[package]
name = "voice_agent"
version = "0.1.0"
edition = "2024"

[dependencies]
whisper_module = { path = "../whisper_module" }
fastrtc_bridge = { path = "../fastrtc_bridge" }
gemini_module = { path = "../gemini_module" }
piper_module = { path = "../piper_module" }
agent_tools = { path = "../agent_tools" }
notify = "6"
tokio = { version = "1", features = ["full"] }
serde_json = "1.0"
serde_yaml = "0.9"
anyhow = "1"
serde = { version = "1.0", features = ["derive"] }
dotenv = "0.15"
