use serde::{Deserialize, Serialize};
use reqwest::StatusCode;

#[derive(Debug)]
pub enum GeminiError {
    Http(reqwest::Error),
    Status(StatusCode),
    Json(serde_json::Error),
    Api(String),
    NoReply,
}

impl From<reqwest::Error> for GeminiError {
    fn from(e: reqwest::Error) -> Self {
        GeminiError::Http(e)
    }
}
impl From<serde_json::Error> for GeminiError {
    fn from(e: serde_json::Error) -> Self {
        GeminiError::Json(e)
    }
}

#[derive(Serialize)]
struct GeminiRequest<'a> {
    contents: [GeminiContent<'a>; 1],
}

#[derive(Serialize)]
struct GeminiContent<'a> {
    parts: [GeminiPart<'a>; 1],
}

#[derive(Serialize)]
struct GeminiPart<'a> {
    text: &'a str,
}

#[derive(Deserialize, Debug)]
struct GeminiResponse {
    candidates: Option<Vec<GeminiCandidate>>,
    #[serde(default)]
    error: Option<GeminiApiError>,
}

#[derive(Deserialize, Debug)]
struct GeminiCandidate {
    content: Option<GeminiContentReply>,
}

#[derive(Deserialize, Debug)]
struct GeminiContentReply {
    parts: Option<Vec<GeminiPartReply>>,
}

#[derive(Deserialize, Debug)]
struct GeminiPartReply {
    text: Option<String>,
}

#[derive(Deserialize, Debug)]
struct GeminiApiError {
    message: Option<String>,
}

pub async fn send_to_gemini(prompt: &str, api_key: &str) -> Result<String, GeminiError> {
    let url = format!("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={}", api_key);
    let req_body = GeminiRequest {
        contents: [GeminiContent {
            parts: [GeminiPart { text: prompt }],
        }],
    };
    let client = reqwest::Client::new();
    let resp = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&req_body)
        .send()
        .await?;
    if !resp.status().is_success() {
        return Err(GeminiError::Status(resp.status()));
    }
    let resp_json: GeminiResponse = resp.json().await?;
    if let Some(err) = resp_json.error {
        return Err(GeminiError::Api(err.message.unwrap_or_else(|| "Unknown API error".to_string())));
    }
    if let Some(candidates) = resp_json.candidates {
        for candidate in candidates {
            if let Some(content) = candidate.content {
                if let Some(parts) = content.parts {
                    for part in parts {
                        if let Some(text) = part.text {
                            return Ok(text);
                        }
                    }
                }
            }
        }
    }
    Err(GeminiError::NoReply)
}

pub fn add(left: u64, right: u64) -> u64 {
    left + right
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let result = add(2, 2);
        assert_eq!(result, 4);
    }
}
