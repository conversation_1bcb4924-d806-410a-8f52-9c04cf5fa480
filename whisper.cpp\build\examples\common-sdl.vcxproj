﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{98DA11A0-C3CB-369A-BE6D-E719E35A7652}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>common-sdl</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">common-sdl.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">common-sdl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">common-sdl.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">common-sdl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">common-sdl.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">common-sdl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">common-sdl.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">common-sdl</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include" /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include/SDL2" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include" /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include/SDL2" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include" /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include/SDL2" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include" /external:I "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/installed/x64-windows/include/SDL2" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4101;4005;4065;4267;4244;4805;4305;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <ForceConformanceInForLoopScope></ForceConformanceInForLoopScope>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RemoveUnreferencedCodeData></RemoveUnreferencedCodeData>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWChar_tAsBuiltInType></TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\include\SDL2;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\common-sdl.h" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\common-sdl.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ZERO_CHECK.vcxproj">
      <Project>{4C435E7B-E811-3F09-974A-C0840C54E5D9}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>