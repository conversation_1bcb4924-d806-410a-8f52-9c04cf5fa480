# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//AVX2 flags
AVX2_FLAGS:STRING=' '

//AVX2 support
AVX2_FOUND:BOOL=TRUE

//AVX512 flags
AVX512_FLAGS:STRING=

//AVX512 support
AVX512_FOUND:BOOL=FALSE

//AVX flags
AVX_FLAGS:STRING=' '

//AVX support
AVX_FOUND:BOOL=TRUE

//ggml: build shared libraries
BUILD_SHARED_LIBS:BOOL=ON

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /W3 /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/MDd /Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/MD /O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/MD /O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/MD /Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS /W3

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/MDd /Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/MD /O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/MD /O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/MD /Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/whisper.cpp

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=whisper.cpp

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.7.6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=7

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=6

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the archiver during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the archiver during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the archiver during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to the coverage program that CTest uses for performing coverage
// inspection
COVERAGE_COMMAND:FILEPATH=COVERAGE_COMMAND-NOTFOUND

//Extra command line flags to pass to the coverage tool
COVERAGE_EXTRA_FLAGS:STRING=-l

//How many times to retry timed-out CTest submissions.
CTEST_SUBMIT_RETRY_COUNT:STRING=3

//How long to wait between timed-out CTest submissions.
CTEST_SUBMIT_RETRY_DELAY:STRING=5

//Maximum time allowed before CTest will kill the test.
DART_TESTING_TIMEOUT:STRING=1500

//FMA flags
FMA_FLAGS:STRING=' '

//FMA support
FMA_FOUND:BOOL=TRUE

//ggml: enable Accelerate framework
GGML_ACCELERATE:BOOL=ON

//ggml: enable all compiler warnings in 3rd party libs
GGML_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//ggml: enable AVX
GGML_AVX:BOOL=OFF

//ggml: enable AVX2
GGML_AVX2:BOOL=OFF

//ggml: enable AVX512F
GGML_AVX512:BOOL=OFF

//ggml: enable AVX512-BF16
GGML_AVX512_BF16:BOOL=OFF

//ggml: enable AVX512-VBMI
GGML_AVX512_VBMI:BOOL=OFF

//ggml: enable AVX512-VNNI
GGML_AVX512_VNNI:BOOL=OFF

//ggml: enable AVX-VNNI
GGML_AVX_VNNI:BOOL=OFF

//ggml: build backends as dynamic libraries (requires BUILD_SHARED_LIBS)
GGML_BACKEND_DL:BOOL=OFF

//Location of binary  files
GGML_BIN_INSTALL_DIR:PATH=bin

//ggml: use BLAS
GGML_BLAS:BOOL=OFF

//ggml: BLAS library vendor
GGML_BLAS_VENDOR:STRING=Generic

//ggml: enable BMI2
GGML_BMI2:BOOL=OFF

//ggml: build examples
GGML_BUILD_EXAMPLES:BOOL=OFF

//ggml: build tests
GGML_BUILD_TESTS:BOOL=OFF

//ggml: use ccache if available
GGML_CCACHE:BOOL=ON

//Path to a program.
GGML_CCACHE_FOUND:FILEPATH=GGML_CCACHE_FOUND-NOTFOUND

//ggml: enable CPU backend
GGML_CPU:BOOL=ON

//ggml: build all variants of the CPU backend (requires GGML_BACKEND_DL)
GGML_CPU_ALL_VARIANTS:BOOL=OFF

//ggml: CPU architecture for ARM
GGML_CPU_ARM_ARCH:STRING=

//ggml: use memkind for CPU HBM
GGML_CPU_HBM:BOOL=OFF

//ggml: use KleidiAI optimized kernels if applicable
GGML_CPU_KLEIDIAI:BOOL=OFF

//ggml: CPU type for PowerPC
GGML_CPU_POWERPC_CPUTYPE:STRING=

//ggml: use runtime weight conversion of Q4_0 to Q4_X_X
GGML_CPU_REPACK:BOOL=ON

//ggml: use CUDA
GGML_CUDA:BOOL=OFF

//ggml: cuda link binary compression mode; requires cuda 12.8+
GGML_CUDA_COMPRESSION_MODE:STRING=size

//ggml: use 16 bit floats for some calculations
GGML_CUDA_F16:BOOL=OFF

//ggml: compile ggml FlashAttention CUDA kernels
GGML_CUDA_FA:BOOL=ON

//ggml: compile all quants for FlashAttention
GGML_CUDA_FA_ALL_QUANTS:BOOL=OFF

//ggml: always use cuBLAS instead of mmq kernels
GGML_CUDA_FORCE_CUBLAS:BOOL=OFF

//ggml: use mmq kernels instead of cuBLAS
GGML_CUDA_FORCE_MMQ:BOOL=OFF

//ggml: use CUDA graphs (llama.cpp only)
GGML_CUDA_GRAPHS:BOOL=OFF

//ggml: do not use peer to peer copies
GGML_CUDA_NO_PEER_COPY:BOOL=OFF

//ggml: do not try to use CUDA VMM
GGML_CUDA_NO_VMM:BOOL=OFF

//ggml: max. batch size for using peer access
GGML_CUDA_PEER_MAX_BATCH_SIZE:STRING=128

//ggml: enable gprof
GGML_GPROF:BOOL=OFF

//ggml: use HIP
GGML_HIP:BOOL=OFF

//ggml: enable rocWMMA FlashAttention on GFX12
GGML_HIP_FORCE_ROCWMMA_FATTN_GFX12:BOOL=OFF

//ggml: use HIP graph, experimental, slow
GGML_HIP_GRAPHS:BOOL=OFF

//ggml: do not try to use HIP VMM
GGML_HIP_NO_VMM:BOOL=ON

//ggml: enable rocWMMA for FlashAttention
GGML_HIP_ROCWMMA_FATTN:BOOL=OFF

//Location of header  files
GGML_INCLUDE_INSTALL_DIR:PATH=include

//ggml: enable lasx
GGML_LASX:BOOL=ON

//Location of library files
GGML_LIB_INSTALL_DIR:PATH=lib

//ggml: use LLAMAFILE
GGML_LLAMAFILE:BOOL=OFF

//ggml: enable lsx
GGML_LSX:BOOL=ON

//ggml: enable link time optimization
GGML_LTO:BOOL=OFF

//ggml: use Metal
GGML_METAL:BOOL=OFF

//ggml: embed Metal library
GGML_METAL_EMBED_LIBRARY:BOOL=OFF

//ggml: metal minimum macOS version
GGML_METAL_MACOSX_VERSION_MIN:STRING=

//ggml: disable Metal debugging
GGML_METAL_NDEBUG:BOOL=OFF

//ggml: compile Metal with -fno-fast-math
GGML_METAL_SHADER_DEBUG:BOOL=OFF

//ggml: metal standard version (-std flag)
GGML_METAL_STD:STRING=

//ggml: use bfloat if available
GGML_METAL_USE_BF16:BOOL=OFF

//ggml: use MUSA
GGML_MUSA:BOOL=OFF

//ggml: optimize the build for the current system
GGML_NATIVE:BOOL=ON

//ggml: enable nnpa
GGML_NNPA:BOOL=ON

//ggml: use OpenCL
GGML_OPENCL:BOOL=OFF

//ggml: embed kernels
GGML_OPENCL_EMBED_KERNELS:BOOL=ON

//ggml: use OpenCL profiling (increases overhead)
GGML_OPENCL_PROFILING:BOOL=OFF

//gmml: OpenCL API version to target
GGML_OPENCL_TARGET_VERSION:STRING=300

//ggml: use optimized kernels for Adreno
GGML_OPENCL_USE_ADRENO_KERNELS:BOOL=ON

//ggml: use OpenMP
GGML_OPENMP:BOOL=ON

//ggml: use RPC
GGML_RPC:BOOL=OFF

//ggml: enable rvv
GGML_RVV:BOOL=ON

//ggml: enable riscv zfh
GGML_RV_ZFH:BOOL=OFF

//Path to a program.
GGML_SCCACHE_FOUND:FILEPATH=GGML_SCCACHE_FOUND-NOTFOUND

//ggml: max input copies for pipeline parallelism
GGML_SCHED_MAX_COPIES:STRING=4

//ggml: enable SSE 4.2
GGML_SSE42:BOOL=OFF

//ggml: static link libraries
GGML_STATIC:BOOL=OFF

//ggml: use SYCL
GGML_SYCL:BOOL=OFF

//ggml: sycl device architecture
GGML_SYCL_DEVICE_ARCH:STRING=

//ggml: enable oneDNN in the SYCL backend
GGML_SYCL_DNN:BOOL=ON

//ggml: use 16 bit floats for sycl calculations
GGML_SYCL_F16:BOOL=OFF

//ggml: enable graphs in the SYCL backend
GGML_SYCL_GRAPH:BOOL=ON

//ggml: sycl target device
GGML_SYCL_TARGET:STRING=INTEL

//ggml: use Vulkan
GGML_VULKAN:BOOL=OFF

//ggml: run Vulkan op checks
GGML_VULKAN_CHECK_RESULTS:BOOL=OFF

//ggml: enable Vulkan debug output
GGML_VULKAN_DEBUG:BOOL=OFF

//ggml: enable Vulkan memory debug output
GGML_VULKAN_MEMORY_DEBUG:BOOL=OFF

//ggml: run Vulkan tests
GGML_VULKAN_RUN_TESTS:BOOL=OFF

//ggml: toolchain file for vulkan-shaders-gen
GGML_VULKAN_SHADERS_GEN_TOOLCHAIN:FILEPATH=

//ggml: enable Vulkan shader debug info
GGML_VULKAN_SHADER_DEBUG_INFO:BOOL=OFF

//ggml: enable Vulkan validation
GGML_VULKAN_VALIDATE:BOOL=OFF

//ggml: enable vxe
GGML_VXE:BOOL=ON

//ggml: enable xtheadvector
GGML_XTHEADVECTOR:BOOL=OFF

//Path to a program.
GITCOMMAND:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Path to a program.
GIT_EXE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Command to build the project
MAKECOMMAND:STRING="C:\Program Files\CMake\bin\cmake.exe" --build . --config "${CTEST_CONFIGURATION_TYPE}"

//Path to a library.
MATH_LIBRARY:FILEPATH=MATH_LIBRARY-NOTFOUND

//Path to the memory checking command, used for memory error detection.
MEMORYCHECK_COMMAND:FILEPATH=MEMORYCHECK_COMMAND-NOTFOUND

//File that contains suppressions for the memory checker
MEMORYCHECK_SUPPRESSIONS_FILE:FILEPATH=

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-openmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-openmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=

//The directory containing a CMake configuration file for SDL2.
SDL2_DIR:PATH=SDL2_DIR-NOTFOUND

//Name of the computer/site where compile is being run
SITE:STRING=Sanny

//whisper: enable all compiler warnings
WHISPER_ALL_WARNINGS:BOOL=ON

//whisper: enable all compiler warnings in 3rd party libs
WHISPER_ALL_WARNINGS_3RD_PARTY:BOOL=OFF

//Location of binary  files
WHISPER_BIN_INSTALL_DIR:PATH=bin

//whisper: build examples
WHISPER_BUILD_EXAMPLES:BOOL=ON

//whisper: build server example
WHISPER_BUILD_SERVER:BOOL=ON

//whisper: build tests
WHISPER_BUILD_TESTS:BOOL=ON

//whisper: enable Core ML framework
WHISPER_COREML:BOOL=OFF

//whisper: allow non-CoreML fallback
WHISPER_COREML_ALLOW_FALLBACK:BOOL=OFF

//whisper: use libcurl to download model from an URL
WHISPER_CURL:BOOL=OFF

//whisper: enable -Werror flag
WHISPER_FATAL_WARNINGS:BOOL=OFF

//Location of header  files
WHISPER_INCLUDE_INSTALL_DIR:PATH=include

//Location of library files
WHISPER_LIB_INSTALL_DIR:PATH=lib

//whisper: support for OpenVINO
WHISPER_OPENVINO:BOOL=OFF

//whisper: enable address sanitizer
WHISPER_SANITIZE_ADDRESS:BOOL=OFF

//whisper: enable thread sanitizer
WHISPER_SANITIZE_THREAD:BOOL=OFF

//whisper: enable undefined sanitizer
WHISPER_SANITIZE_UNDEFINED:BOOL=OFF

//whisper: support for libSDL2
WHISPER_SDL2:BOOL=ON

//whisper: use system-installed GGML library
WHISPER_USE_SYSTEM_GGML:BOOL=OFF

//Dependencies for the target
common_LIB_DEPENDS:STATIC=general;whisper;

//Value Computed by CMake
ggml_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml

//Value Computed by CMake
ggml_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
ggml_SOURCE_DIR:STATIC=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/ggml

//Value Computed by CMake
whisper.cpp_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build

//Value Computed by CMake
whisper.cpp_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
whisper.cpp_SOURCE_DIR:STATIC=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp

//Dependencies for the target
whisper_LIB_DEPENDS:STATIC=general;ggml;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: AVX2_FLAGS
AVX2_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: AVX2_FOUND
AVX2_FOUND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: AVX512_FLAGS
AVX512_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: AVX512_FOUND
AVX512_FOUND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: AVX_FLAGS
AVX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: AVX_FOUND
AVX_FOUND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//ADVANCED property for variable: CMAKE_CTEST_COMMAND
CMAKE_CTEST_COMMAND-ADVANCED:INTERNAL=1
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Preview
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=7
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-4.1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_COMMAND
COVERAGE_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_EXTRA_FLAGS
COVERAGE_EXTRA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_COUNT
CTEST_SUBMIT_RETRY_COUNT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_DELAY
CTEST_SUBMIT_RETRY_DELAY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DART_TESTING_TIMEOUT
DART_TESTING_TIMEOUT-ADVANCED:INTERNAL=1
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[C:/Program Files/Git/cmd/git.exe][v2.50.1.windows.1()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][ ][v2.0()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-openmp][v2.0()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-openmp][v2.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: FMA_FLAGS
FMA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FMA_FOUND
FMA_FOUND-ADVANCED:INTERNAL=1
//List of backends for cmake package
GGML_AVAILABLE_BACKENDS:INTERNAL=ggml-cpu
//STRINGS property for variable: GGML_CUDA_COMPRESSION_MODE
GGML_CUDA_COMPRESSION_MODE-STRINGS:INTERNAL=none;speed;balance;size
//ADVANCED property for variable: GITCOMMAND
GITCOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Test HAS_AVX2_1
HAS_AVX2_1:INTERNAL=1
//Result of TRY_COMPILE
HAS_AVX2_1_COMPILED:INTERNAL=TRUE
//Result of try_run()
HAS_AVX2_1_EXITCODE:INTERNAL=0
//Test HAS_AVX512_1
HAS_AVX512_1:INTERNAL=
//Result of TRY_COMPILE
HAS_AVX512_1_COMPILED:INTERNAL=TRUE
//Result of try_run()
HAS_AVX512_1_EXITCODE:INTERNAL=FAILED_TO_RUN
//Test HAS_AVX512_2
HAS_AVX512_2:INTERNAL=
//Result of TRY_COMPILE
HAS_AVX512_2_COMPILED:INTERNAL=TRUE
//Result of try_run()
HAS_AVX512_2_EXITCODE:INTERNAL=FAILED_TO_RUN
//Test HAS_AVX_1
HAS_AVX_1:INTERNAL=1
//Result of TRY_COMPILE
HAS_AVX_1_COMPILED:INTERNAL=TRUE
//Result of try_run()
HAS_AVX_1_EXITCODE:INTERNAL=0
//Test HAS_FMA_1
HAS_FMA_1:INTERNAL=1
//Result of TRY_COMPILE
HAS_FMA_1_COMPILED:INTERNAL=TRUE
//Result of try_run()
HAS_FMA_1_EXITCODE:INTERNAL=0
//ADVANCED property for variable: MAKECOMMAND
MAKECOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_COMMAND
MEMORYCHECK_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_SUPPRESSIONS_FILE
MEMORYCHECK_SUPPRESSIONS_FILE-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_openmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_openmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=200203
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=200203
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: SITE
SITE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files (x86)/whisper.cpp

