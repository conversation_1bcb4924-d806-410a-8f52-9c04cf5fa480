#line 1 "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\vcpkg\\buildtrees\\sdl2\\src\\ase-2.32.8-cdefe94ced.clean\\src\\main\\windows\\version.rc"

#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"





















#line 23 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"



#line 27 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"



#line 31 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"



#line 35 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"

#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


















#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"



















#pragma warning(push)
#pragma warning(disable:4001) 
#line 23 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"
#pragma once
#line 25 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"

#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"



















#pragma warning(push)
#pragma warning(disable:4001) 
#line 23 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"
#pragma once
#line 25 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"



#line 29 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"


























































#pragma warning(pop)
#line 89 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"
#line 90 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"

#line 92 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winpackagefamily.h"
#line 27 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"































































#line 91 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"



















































#line 148 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"



#line 152 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"






#line 159 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"





#line 165 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"



#line 169 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"





#line 175 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"











#line 187 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"






































 

















#line 244 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"



#pragma warning(pop)
#line 249 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"
#line 250 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"

#line 252 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\winapifamily.h"
#line 20 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



























































#line 80 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




































































































































#line 213 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"























































#line 269 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
















































#line 318 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






















#line 341 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






#line 348 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




































































#line 417 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"







#line 425 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 428 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"































#line 460 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 461 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"







































#line 501 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"










#line 512 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

















#line 530 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 535 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 539 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"














#line 554 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

#line 556 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






#line 563 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"












#line 576 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"































#line 608 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 609 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 610 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

#line 612 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 613 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"























#line 637 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 642 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 645 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 649 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





#line 655 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"









#line 665 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"










#line 676 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"











#line 688 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 691 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"























#line 715 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 720 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



















#line 740 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"









#line 750 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 753 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 757 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 762 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 766 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






#line 773 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 777 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 782 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
























#line 807 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 811 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 815 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 820 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






#line 827 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 832 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 837 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 840 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"








#line 849 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"







#line 857 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




















#line 878 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




































#line 915 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"










#line 926 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 929 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

#line 931 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

#line 933 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
















#line 950 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"















































#line 998 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

#line 1000 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"









#line 1010 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





#line 1016 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1019 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 1020 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
































































#line 1085 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

















#line 1103 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1108 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1113 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1117 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1121 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1125 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1128 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




















#line 1149 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"























#line 1173 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1176 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"







#line 1184 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
























































#line 1241 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1246 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1251 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






















#line 1274 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


























#line 1301 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1305 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


































































#line 1372 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1375 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"












#line 1388 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1391 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"













#line 1405 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1408 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"












#line 1421 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





#line 1427 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"















#line 1443 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1448 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





#line 1454 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




















#line 1475 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"











































#line 1519 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"




#line 1524 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1528 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



































#line 1564 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
















#line 1581 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"













#line 1595 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1598 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"







#line 1606 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

























#line 1632 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"


#line 1635 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"












#line 1648 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"

























#line 1674 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"



#line 1678 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





















#line 1700 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"






















#line 1723 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
















#line 1740 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"





































#line 1778 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winuser.rh"
#line 37 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"



























#line 29 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"





#line 35 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"


















































#line 86 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"




























#line 115 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"



#line 119 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"



















#line 139 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"



#line 143 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"
























#line 168 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"

















































#line 218 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"












#line 231 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"


#line 234 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"







































#line 274 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\commctrl.rh"














































#line 38 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dde.rh"






























#line 39 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winnt.rh"



















#line 21 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winnt.rh"



































































































































































































































































































































































































































































































































#line 40 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"
















#pragma once





#pragma region Desktop Family






























































































































































































































#line 247 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"




#line 252 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"





#line 258 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"
















#line 275 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"
#pragma endregion


#line 279 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\dlgs.h"
#line 41 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"

















#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
















#pragma once
#line 19 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"


#pragma warning(push)
#pragma warning(disable:4668) 
#line 24 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"



 

#line 30 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
  
 #line 32 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

 



  
 #line 39 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

 
  
  
 #line 44 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

#line 46 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"







#line 54 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

#line 56 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

#line 58 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"



#line 1 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"













#pragma once







































































































































#line 151 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"



#line 155 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"





























#line 185 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"


#line 188 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"

#line 190 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"





#line 196 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"



#line 200 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






#line 207 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"











#line 219 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"









#line 229 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"































































































































































































































































































































































































































































































#pragma region Input Buffer SAL 1 compatibility macros



























































































































































































































































































































































































































































































































































































































































































































































































#pragma endregion Input Buffer SAL 1 compatibility macros

















































































#line 1555 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






























#line 1586 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
























#line 1611 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"












#line 1624 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






































#line 1663 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"















































































































#line 1775 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






































































































#line 1878 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"








































































































































































#line 2047 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"





































































































#line 2149 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
























































































































































































































#line 2366 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
#line 2367 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"



































































































































































































































#line 2595 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    

    
    

#line 2634 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"









































































































































































































































#line 2868 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"









#line 2878 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"



    
    


#line 2886 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
#line 2887 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






#line 2894 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
#line 2895 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"






#line 2902 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
#line 2903 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"











#line 2915 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"

































#line 2949 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"

























#line 1 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\concurrencysal.h"


















#pragma once















































































































































































































































































#line 292 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\concurrencysal.h"



#line 296 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\concurrencysal.h"




























































































#line 389 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\concurrencysal.h"





#line 395 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\concurrencysal.h"
#line 2975 "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include\\sal.h"
#line 62 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"




#line 67 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
































#line 100 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"












#line 113 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"







































































































#line 217 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"






































































































#line 320 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"



























































#line 380 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"























































































#line 468 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"



















#line 488 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"





#line 494 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"


#line 497 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"




































































#line 566 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"










#line 577 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"






























#line 608 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
















#line 625 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

















#line 643 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
































#line 676 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

















#line 694 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"




























































































































#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\sdv_driverspecs.h"





















#line 23 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\sdv_driverspecs.h"
#line 126 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"



#pragma once
#line 131 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"



























































































































#line 255 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"

    
    
    
    
    
    
    
    
    
    
    

    
    

#line 272 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"

    
    
    



    
    
    
    
    

    
    
    
    
    
    
    

    
    
    
    



    
    


    
    
    
    
    
    
    
    
    
    
    
    


    
    


    
    


    
    



    
    









    
    
    
    
    
    
    
    
    
    

    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    


    


    
    
    
    
    

    


    
    
    
    
    

    


    
    
    
    
    

    


    
    
    
    
    


    




    
    
    
    
    

    


    
    
    
    
    


    


    
    
    
    
    
    

    


    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    

    
    
    
    
    

    
    
    
    
    

    
    
    
    
    

    
    
    
    
    

    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    


    
    

    
    
    
    

    

    
    
    

    

    
    
    
    
    
    
    
    
    
    
    
    

    
    


    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    

    
    
    
    
    
    


    
    
    
    
     
    

    
    
        
        
        
        
    
    
    
    
    
    

    
    
    

    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    
    

    

    
    
    
    
    
    
    
    
    
    
    
#line 699 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"
    
#line 701 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"
    


    
    
    
    
    
    


    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    

    
    
    
    
    
    

    
    
    
    
    
    
    
    
    

    
    

    
    

    
    
    
    
    
    


    
    
    

    
	

    
    


    
    


    
    
    


    
    
    


    
    











































































    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

#line 887 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"

    
    
    

    
    
    





#line 901 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\driverspecs.h"

#line 696 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"












#line 709 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"


#pragma warning(pop)
#line 713 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"

#line 715 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\shared\\SpecStrings.h"
#line 19 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#line 1 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\verrsrc.h"












#pragma region Application Family or OneCore Family or Games Family












#line 27 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\verrsrc.h"




























































#line 88 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\verrsrc.h"
#pragma endregion

#pragma region Desktop Family or OneCore Family or Games Family















































































#line 171 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\verrsrc.h"
#pragma endregion

#line 20 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"






#pragma region Desktop Family or OneCore Family







































#line 67 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#pragma endregion

#pragma region Desktop Family




































#line 107 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#pragma endregion

#pragma region Application Family or OneCore Family










































































#line 185 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#pragma endregion

#pragma region Desktop Family







#line 196 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#pragma endregion

#pragma region Application Family or OneCore Family

















































#line 249 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"
#pragma endregion






#line 257 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winver.h"

#line 42 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"

#line 44 "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22000.0\\um\\winresrc.h"
#line 3 "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\vcpkg\\buildtrees\\sdl2\\src\\ase-2.32.8-cdefe94ced.clean\\src\\main\\windows\\version.rc"

LANGUAGE 0x09, 0x01






1 VERSIONINFO
 FILEVERSION 2,32,8,0
 PRODUCTVERSION 2,32,8,0
 FILEFLAGSMASK 0x3fL
 FILEFLAGS 0x0L
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "\0"
            VALUE "FileDescription", "SDL\0"
            VALUE "FileVersion", "2, 32, 8, 0\0"
            VALUE "InternalName", "SDL\0"
            VALUE "LegalCopyright", "Copyright (C) 2025 Sam Lantinga\0"
            VALUE "OriginalFilename", "SDL2.dll\0"
            VALUE "ProductName", "Simple DirectMedia Layer\0"
            VALUE "ProductVersion", "2, 32, 8, 0\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
