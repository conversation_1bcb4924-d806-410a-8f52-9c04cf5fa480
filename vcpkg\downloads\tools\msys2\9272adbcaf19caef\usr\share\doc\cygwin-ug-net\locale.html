<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>locale</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="ldd.html" title="ldd"><link rel="next" href="lsattr.html" title="lsattr"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">locale</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="ldd.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="lsattr.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="locale"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>locale &#8212; Get locale-specific information</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">locale</code>  [-amvhV]</p></div><div class="cmdsynopsis"><p><code class="command">locale</code>  [-ck]  <em class="replaceable"><code>NAME</code></em> </p></div><div class="cmdsynopsis"><p><code class="command">locale</code>  [-iusfnU]</p></div><div class="cmdsynopsis"><p><code class="command">locale</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="locale-options"></a><h2>Options</h2><pre class="screen">
System information:

  -a, --all-locales    List all available supported locales
  -m, --charmaps       List all available character maps
  -v, --verbose        More verbose output

Modify output format:

  -c, --category-name  List information about given category NAME
  -k, --keyword-name   Print information about given keyword NAME

Default locale information:

  -i, --input          Print current input locale
  -u, --user           Print locale of user's default UI language
  -s, --system         Print locale of system default UI language
  -f, --format         Print locale of user's regional format settings
                       (time, numeric &amp; monetary)
  -n, --no-unicode     Print system default locale for non-Unicode programs
  -U, --utf            Attach \".UTF-8\" to the result

Other options:

  -h, --help           This text
  -V, --version        Print program version and exit
</pre></div><div class="refsect1"><a name="locale-desc"></a><h2>Description</h2><p><span class="command"><strong>locale</strong></span> without parameters prints information about
      the current locale environment settings.</p><p>The <code class="literal">-i</code>, <code class="literal">-u</code>,
      <code class="literal">-s</code>, <code class="literal">-f</code>, and <code class="literal">-n</code>
      options can be used to request the various Windows locale settings. The
      purpose is to use this command in scripts to set the POSIX locale
      variables.</p><p>The <code class="literal">-i</code> option prints the current input language.
      This is called the "Input language" and basically equivalent to the
      current keyboard layout setting.</p><p>The <code class="literal">-u</code> option prints the current user's Windows UI
      locale to stdout. In Windows this setting is called the
      "Display Language".</p><p>The <code class="literal">-s</code> option prints the systems default instead.
    </p><p>The <code class="literal">-f</code> option prints the user's setting for time,
      date, number and currency. That's equivalent to the setting in the
      "Formats" or "Regional Options" tab in the "Region and Language" or
      "Regional and Language Options" dialog.</p><p>The <code class="literal">-n</code> option prints the system's default
      language used for applications which don't support Unicode.</p><p>With the <code class="literal">-U</code> option <span class="command"><strong>locale</strong></span>
      appends the string ".UTF-8" to enforce using UTF-8.  Using UTF-8
      as codeset is recommended.</p><p>Usage example:</p><pre class="screen">
bash$ export LANG=$(locale -uU)
bash$ echo $LANG
en_US.UTF-8
bash$ export LC_TIME=$(locale -fU)
bash$ echo $LC_TIME
de_DE.UTF-8
</pre><p>The <code class="literal">-a</code> option is helpful to learn which locales
      are supported by your Windows machine. It prints all available locales
      and the allowed modifiers. Example:</p><pre class="screen">
bash$ locale -a
C
C.utf8
POSIX
af_ZA
af_ZA.utf8
am_ET
am_ET.utf8
...
be_BY
be_BY.utf8
be_BY@latin
...
ca_ES
ca_ES.utf8
ca_ES@euro
catalan
...
</pre><p>The <code class="literal">-v</code> option prints more detailed information
      about each available locale. Example:</p><pre class="screen">
bash$ locale -av
locale: af_ZA           archive: /cygdrive/c/Windows/system32/kernel32.dll
-------------------------------------------------------------------------------
 language | Afrikaans
territory | South Africa
  codeset | ISO-8859-1

locale: af_ZA.utf8      archive: /cygdrive/c/Windows/system32/kernel32.dll
-------------------------------------------------------------------------------
 language | Afrikaans
territory | South Africa
  codeset | UTF-8

...

locale: ca_ES@euro      archive: /cygdrive/c/Windows/system32/kernel32.dll
-------------------------------------------------------------------------------
 language | Catalan
territory | Spain
  codeset | ISO-8859-15

locale: catalan         archive: /usr/share/locale/locale.alias
-------------------------------------------------------------------------------
 language | Catalan
territory | Spain
  codeset | ISO-8859-1

...
</pre><p>The <code class="literal">-m</code> option prints the names of the available
      charmaps supported by Cygwin to stdout.</p><p>Otherwise, if arguments are given, <span class="command"><strong>locale</strong></span> prints
      the values assigned to these arguments. Arguments can be names of locale
      categories (for instance: LC_CTYPE, LC_MONETARY), or names of keywords
      supported in the locale categories (for instance: thousands_sep,
      charmap). The <code class="literal">-c</code> option prints additionally the name
      of the category. The <code class="literal">-k</code> option prints additionally the
      name of the keyword. Example:</p><pre class="screen">
bash$ locale -ck LC_MESSAGES
LC_MESSAGES
yesexpr="^[yY]"
noexpr="^[nN]"
yesstr="yes"
nostr="no"
messages-codeset="UTF-8"
bash$ locale noexpr
^[nN]
    </pre></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="ldd.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="lsattr.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">ldd&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;lsattr</td></tr></table></div></body></html>
