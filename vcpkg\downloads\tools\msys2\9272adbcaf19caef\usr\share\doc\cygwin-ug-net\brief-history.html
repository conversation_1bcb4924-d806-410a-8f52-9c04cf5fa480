<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>A brief history of the Cygwin project</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"><link rel="prev" href="are-free.html" title="Are the Cygwin tools free software?"><link rel="next" href="highlights.html" title="Highlights of Cygwin Functionality"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">A brief history of the Cygwin project</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="are-free.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Cygwin Overview</th><td width="20%" align="right">&#160;<a accesskey="n" href="highlights.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="brief-history"></a>A brief history of the Cygwin project</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
A historical look into the first years of Cygwin development is
Geoffrey J. Noer's 1998 paper, "Cygwin32: A Free Win32 Porting Layer for
UNIX&#174; Applications" which can be found at the <a class="ulink" href="http://www.usenix.org/publications/library/proceedings/usenix-nt98/technical.html" target="_top">
2nd USENIX Windows NT Symposium Online Proceedings</a>.
</p></div><p>
Cygwin began development in 1995 at Cygnus Solutions (now part of Red Hat,
Inc.).  The first thing done was to enhance the development tools 
(<span class="command"><strong>gcc</strong></span>, <span class="command"><strong>gdb</strong></span>, <span class="command"><strong>gas</strong></span>, 
etc.) so that they could generate and interpret Win32 native
object files.
The next task was to port the tools to Win NT/9x. We could have
done this by rewriting large portions of the source to work within the
context of the Win32 API. But this would have meant spending a huge
amount of time on each and every tool. Instead, we took a
substantially different approach by writing a shared library
(the Cygwin DLL) that adds the necessary UNIX-like functionality
missing from the Win32 API (<code class="function">fork</code>,
<code class="function">spawn</code>, <code class="function">signals</code>,
<code class="function">select</code>, <code class="function">sockets</code>, etc.).  We call this
new interface the Cygwin API. Once written, it was possible to build working
Win32 tools using UNIX-hosted cross-compilers, linking against this
library.</p><p>From this point, we pursued the goal of producing Windows-hosted tools
capable of rebuilding themselves under Windows 9x and NT (this is
often called self-hosting). Since neither OS ships with standard UNIX
user tools (fileutils, textutils, bash, etc...), we had to get the GNU
equivalents working with the Cygwin API. Many of these tools were
previously only built natively so we had to modify their configure
scripts to be compatible with cross-compilation. Other than the
configuration changes, very few source-level changes had to be
made since Cygwin provided a UNIX-like API. Running bash with the development tools and user tools in place,
Windows 9x and NT looked like a flavor of UNIX from the perspective of
the GNU configure mechanism. Self hosting was achieved as of the beta
17.1 release in October 1996.</p><p>
The entire Cygwin toolset was available as a monolithic install. In
April 2000, the project announced a
<a class="ulink" href="https://www.cygwin.com/ml/cygwin/2000-04/msg00269.html" target="_top">
New Cygwin Net Release</a> which provided a graphical non-Cygwin
Setup program to install and upgrade each package separately. Since then,
the Cygwin DLL and the Setup tool have seen continuous development.
</p><p>
The biggest major improvement in this development was the 1.7 release in
2009, which dropped Windows 95/98/Me support in favor of using Windows
NT features more extensively.  It adds a lot of new features like
case-sensitive filenames, NFS interoperability, IPv6 support and much
more.</p><p>The latest big improvement is the 64 bit Cygwin DLL which
allows to run natively on AMD64 Windows machines.  The first release
available in a 64 bit version was 1.7.19.</p></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="are-free.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="overview.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="highlights.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Are the Cygwin tools free software?&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Highlights of Cygwin Functionality</td></tr></table></div></body></html>
