<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Chapter&#160;1.&#160;Cygwin Overview</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="prev" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="next" href="ov-ex-win.html" title="Quick Start Guide for those more experienced with Windows"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Chapter&#160;1.&#160;Cygwin Overview</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="cygwin-ug-net.html">Prev</a>&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="ov-ex-win.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h1 class="title"><a name="overview"></a>Chapter&#160;1.&#160;Cygwin Overview</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="sect1"><a href="overview.html#what-is-it">What is it?</a></span></dt><dt><span class="sect1"><a href="ov-ex-win.html">Quick Start Guide for those more experienced with Windows</a></span></dt><dt><span class="sect1"><a href="ov-ex-unix.html">Quick Start Guide for those more experienced with UNIX</a></span></dt><dt><span class="sect1"><a href="are-free.html">Are the Cygwin tools free software?</a></span></dt><dt><span class="sect1"><a href="brief-history.html">A brief history of the Cygwin project</a></span></dt><dt><span class="sect1"><a href="highlights.html">Highlights of Cygwin Functionality</a></span></dt><dd><dl><dt><span class="sect2"><a href="highlights.html#ov-hi-intro">Introduction</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-perm">Permissions and Security</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-files">File Access</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-textvsbinary">Text Mode vs. Binary Mode</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-ansiclib">ANSI C Library</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-process">Process Creation</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-signals">Signals</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-sockets">Sockets</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-select">Select</a></span></dt></dl></dd><dt><span class="sect1"><a href="ov-new.html">What's new and what changed in Cygwin</a></span></dt><dd><dl><dt><span class="sect2"><a href="ov-new.html#ov-new3.6">What's new and what changed in 3.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.5">What's new and what changed in 3.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.4">What's new and what changed in 3.4</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.3">What's new and what changed in 3.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.2">What's new and what changed in 3.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.1">What's new and what changed in 3.1</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.0">What's new and what changed in 3.0</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.11">What's new and what changed in 2.11</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.10">What's new and what changed in 2.10</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.9">What's new and what changed in 2.9</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.8">What's new and what changed in 2.8</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.7">What's new and what changed in 2.7</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.6">What's new and what changed in 2.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.5">What's new and what changed in 2.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.4">What's new and what changed in 2.4</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.3">What's new and what changed in 2.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.2">What's new and what changed in 2.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.1">What's new and what changed in 2.1</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.0">What's new and what changed in 2.0</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.35">What's new and what changed in 1.7.35</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.34">What's new and what changed in 1.7.34</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.33">What's new and what changed in 1.7.33</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.32">What's new and what changed in 1.7.32</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.31">What's new and what changed in 1.7.31</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.29">What's new and what changed in 1.7.29</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.28">What's new and what changed in 1.7.28</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.27">What's new and what changed in 1.7.27</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.26">What's new and what changed in 1.7.26</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.25">What's new and what changed in 1.7.25</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.24">What's new and what changed in 1.7.24</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.23">What's new and what changed in 1.7.23</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.22">What's new and what changed in 1.7.22</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.21">What's new and what changed in 1.7.21</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.19">What's new and what changed in 1.7.19</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.18">What's new and what changed in 1.7.18</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.17">What's new and what changed in 1.7.17</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.16">What's new and what changed in 1.7.16</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.15">What's new and what changed in 1.7.15</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.14">What's new and what changed in 1.7.14</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.13">What's new and what changed in 1.7.13</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.12">What's new and what changed in 1.7.12</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.11">What's new and what changed in 1.7.11</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.10">What's new and what changed in 1.7.10</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.9">What's new and what changed in 1.7.9</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.8">What's new and what changed in 1.7.8</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.7">What's new and what changed in 1.7.7</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.6">What's new and what changed in 1.7.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.5">What's new and what changed in 1.7.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.3">What's new and what changed in 1.7.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.2">What's new and what changed in 1.7.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.1">What's new and what changed from 1.5 to 1.7</a></span></dt><dd><dl><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-os">OS related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-file">File Access related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-net">Network related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-device">Device related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-posix">Other POSIX related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-sec">Security related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-misc">Miscellaneous</a></span></dt></dl></dd></dl></dd></dl></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="what-is-it"></a>What is it?</h2></div></div></div><p>
Cygwin is a Linux-like environment for Windows. It consists of a DLL
(<code class="filename">cygwin1.dll</code>), which acts as an emulation layer
providing substantial <a class="ulink" href="http://www.pasc.org/#POSIX" target="_top">POSIX</a>
(Portable Operating System Interface) system call functionality, and a
collection of tools, which provide a Linux look and feel. The Cygwin DLL
works with all AMD64 versions of Windows NT since Windows Vista/Server 2008.
The API follows the
<a class="ulink" href="http://www.opengroup.org/onlinepubs/009695399/nfindex.html" target="_top">Single
Unix Specification</a> as much as possible, and then Linux practice.
The major differences between Cygwin and Linux is the C library
(<code class="literal">newlib</code> instead of <code class="literal">glibc</code>).
</p><p>
With Cygwin installed, users have access to many standard UNIX
utilities.  They can be used from one of the provided shells such
as <span class="command"><strong>bash</strong></span> or from the Windows Command Prompt.
Additionally, programmers may write Win32 console or GUI applications
that make use of the standard Microsoft Win32 API and/or the Cygwin API.
As a result, it is possible to easily port many significant UNIX
programs without the need for extensive changes to the source code.
This includes configuring and building most of the available GNU
software (including the development tools included with the Cygwin
distribution). 
</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="cygwin-ug-net.html">Prev</a>&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="ov-ex-win.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Cygwin User's Guide&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Quick Start Guide for those more experienced with Windows</td></tr></table></div></body></html>
