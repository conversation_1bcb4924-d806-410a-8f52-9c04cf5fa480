{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 8277825411580366689, "profile": 17672942494452627365, "path": 17266458284092215070, "deps": [[1216658119993006735, "piper_module", false, 13689254193584107954], [1760623714118191065, "dotenv", false, 4351853014253472390], [1994437411054725675, "gemini_module", false, 13771259238847303422], [2941951222343019209, "notify", false, 3721811330894123405], [5265161007890236711, "fastrtc_bridge", false, 17885182976315410628], [9614479274285663593, "serde_yaml", false, 9581852304722162298], [9689903380558560274, "serde", false, 3932883301695986267], [12393800526703971956, "tokio", false, 11632526497277506854], [13625485746686963219, "anyhow", false, 2051588865267231834], [14791524771484756873, "whisper_module", false, 17015893920826611913], [15367738274754116744, "serde_json", false, 2136575339520338002], [17907286668138153651, "agent_tools", false, 14299613649473930911]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\voice_agent-efb96ddfee83a812\\dep-bin-voice_agent", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}