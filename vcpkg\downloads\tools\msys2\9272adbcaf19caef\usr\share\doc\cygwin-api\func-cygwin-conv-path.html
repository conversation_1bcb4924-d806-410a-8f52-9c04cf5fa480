<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_conv_path</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-functions.html#func-cygwin-path" title="Path conversion functions"><link rel="prev" href="cygwin-functions.html" title="Chapter&#160;2.&#160;Cygwin Functions"><link rel="next" href="func-cygwin-conv-path-list.html" title="cygwin_conv_path_list"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_conv_path</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="cygwin-functions.html">Prev</a>&#160;</td><th width="60%" align="center">Path conversion functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-conv-path-list.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-conv-path"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_conv_path</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">ssize_t
<b class="fsfunc">cygwin_conv_path</b>(</code>cygwin_conv_path_t <var class="pdparam">what</var>, const void * <var class="pdparam">from</var>, void * <var class="pdparam">to</var>, size_t <var class="pdparam">size</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-conv-path-desc"></a><h2>Description</h2><p>Use this function to convert NUL-terminated POSIX paths in
<em class="parameter"><code>from</code></em> to NUL-terminated Win32 paths in
<em class="parameter"><code>to</code></em> or, vice versa, NUL-terminated Win32 paths in
<em class="parameter"><code>from</code></em> to NUL-terminated POSIX paths in
<em class="parameter"><code>to</code></em>.  <em class="parameter"><code>what</code></em> defines the
direction of this conversion and can be any of the below values.</p><pre class="programlisting">
  CCP_POSIX_TO_WIN_A      /* from is char *posix, to is char *win32       */
  CCP_POSIX_TO_WIN_W,     /* from is char *posix, to is wchar_t *win32    */
  CCP_WIN_A_TO_POSIX,     /* from is char *win32, to is char *posix       */
  CCP_WIN_W_TO_POSIX,     /* from is wchar_t *win32, to is char *posix    */
</pre><p>You can additionally or the following values to
<em class="parameter"><code>what</code></em>, to define whether you want the resulting
path in <em class="parameter"><code>to</code></em> to be absolute or if you want to keep
relative paths in relative notation.  Creating absolute paths is the
default.</p><pre class="programlisting">
  CCP_ABSOLUTE = 0,         /* Request absolute path (default).             */
  CCP_RELATIVE = 0x100      /* Request to keep path relative.               */
  CCP_PROC_CYGDRIVE = 0x200 /* Request to return /proc/cygdrive path
                               (only with CCP_*_TO_POSIX).                  */
</pre><p><em class="parameter"><code>size</code></em> is the size of the buffer pointed to
by <em class="parameter"><code>to</code></em> in bytes.  If <em class="parameter"><code>size</code></em>
is 0, <em class="parameter"><code>to</code></em> may be NULL and
<code class="function">cygwin_conv_path</code> just returns the required
buffer size in bytes.  Otherwise, it returns 0 on success, or -1 on
error and errno is set to one of the below values.</p><pre class="programlisting">
    EINVAL        what has an invalid value or from is NULL.
    EFAULT        from or to point into nirvana.
    ENAMETOOLONG  the resulting path is longer than 32K, or, in case
                  of what == CCP_POSIX_TO_WIN_A, longer than MAX_PATH.
    ENOSPC        size is less than required for the conversion.
</pre><p>In the event of an error, the memory at <em class="parameter"><code>to</code></em> is
not modified unless the error is <code class="constant">EFAULT</code> writing to
the memory at <em class="parameter"><code>to</code></em>, which may happen if
<em class="parameter"><code>size</code></em> is incorrectly specified.</p></div><div class="refsect1"><a name="func-cygwin-conv-path-example"></a><h2>Example</h2><div class="example"><a name="func-cygwin-conv-path-example-example"></a><p class="title"><b>Example&#160;2.1.&#160;Example use of cygwin_conv_path</b></p><div class="example-contents"><pre class="programlisting">

#include &lt;sys/cygwin.h&gt;

/* Conversion from incoming Win32 path given as wchar_t *win32 to POSIX path.
   If incoming path is a relative path, stick to it.  First ask how big
   the output buffer has to be and allocate space dynamically. */
ssize_t size;
char *posix;
size = cygwin_conv_path (CCP_WIN_W_TO_POSIX | CCP_RELATIVE, win32, NULL, 0);
if (size &lt; 0)
  perror ("cygwin_conv_path");
else
  {
    posix = (char *) malloc (size);
    if (cygwin_conv_path (CCP_WIN_W_TO_POSIX | CCP_RELATIVE, win32,
                          posix, size))
      perror ("cygwin_conv_path");
  }

</pre></div></div><br class="example-break"></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="cygwin-functions.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="cygwin-functions.html#func-cygwin-path">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-conv-path-list.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Chapter&#160;2.&#160;Cygwin Functions&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_conv_path_list</td></tr></table></div></body></html>
