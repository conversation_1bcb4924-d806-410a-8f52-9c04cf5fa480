{"rustc": 1842507548689473721, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 2241668132362809309, "path": 1043756640006038668, "deps": [[1999565553139417705, "windows_sys", false, 17913178403993046553], [3869670940427635694, "filetime", false, 402751147525370419], [4684437522915235464, "libc", false, 7485522700002396042], [5986029879202738730, "log", false, 18342494332034957139], [9727213718512686088, "crossbeam_channel", false, 8485594570262053058], [15622660310229662834, "walkdir", false, 12676695269983663453]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-56d661cb53203014\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}