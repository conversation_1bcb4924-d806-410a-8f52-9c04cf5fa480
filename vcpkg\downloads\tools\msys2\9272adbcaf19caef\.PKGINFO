# Generated by makepkg 6.1.0
pkgname = msys2-runtime
pkgbase = msys2-runtime
xdata = pkgtype=split
pkgver = 3.6.2-2
pkgdesc = Posix emulation engine for Windows
url = https://www.cygwin.com/
builddate = **********
packager = CI (msys2/msys2-autobuild/5c250470/15413199460)
size = 6653973
arch = x86_64
license = GPL
replaces = catgets
replaces = libcatgets
replaces = msys2-runtime-3.6
conflict = catgets
conflict = libcatgets
conflict = msys2-runtime-3.6
makedepend = cocom
makedepend = git
makedepend = perl
makedepend = gcc
makedepend = mingw-w64-cross-crt
makedepend = mingw-w64-cross-gcc
makedepend = mingw-w64-cross-zlib
makedepend = zlib-devel
makedepend = gettext-devel
makedepend = libiconv-devel
makedepend = autotools
makedepend = xmlto
makedepend = docbook-xsl
