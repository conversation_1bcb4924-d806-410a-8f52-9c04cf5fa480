﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-alloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-backend.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-opt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-threading.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-quants.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\gguf.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\ggml.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\ggml-alloc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\ggml-backend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\ggml-cpp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\ggml-opt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\include\gguf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-threading.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-quants.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{3935DB6D-7037-38D7-A0EE-161DF4299B1B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{CDC56093-E337-36CB-BEF2-4F74AFE8CC5E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
