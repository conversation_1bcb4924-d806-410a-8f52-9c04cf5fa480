<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Chapter&#160;1.&#160;Compatibility</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-api.html" title="Cygwin API Reference"><link rel="prev" href="cygwin-api.html" title="Cygwin API Reference"><link rel="next" href="std-bsd.html" title="System interfaces compatible with BSD functions:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Chapter&#160;1.&#160;Compatibility</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="cygwin-api.html">Prev</a>&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-bsd.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h1 class="title"><a name="compatibility"></a>Chapter&#160;1.&#160;Compatibility</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="sect1"><a href="compatibility.html#std-susv5">System interfaces compatible with the Single UNIX&#174; Specification Version 5:</a></span></dt><dt><span class="sect1"><a href="std-bsd.html">System interfaces compatible with BSD functions:</a></span></dt><dt><span class="sect1"><a href="std-gnu.html">System interfaces compatible with GNU or Linux extensions:</a></span></dt><dt><span class="sect1"><a href="std-solaris.html">System interfaces compatible with Solaris or SunOS functions:</a></span></dt><dt><span class="sect1"><a href="std-iso.html">System interfaces not in POSIX but compatible with ISO C requirements:</a></span></dt><dt><span class="sect1"><a href="std-deprec.html">Other UNIX&#174; system interfaces, not in POSIX.1-2024, or deprecated:</a></span></dt><dt><span class="sect1"><a href="std-notimpl.html">NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:</a></span></dt><dt><span class="sect1"><a href="std-other.html">Other system interfaces, some from Windows:</a></span></dt><dt><span class="sect1"><a href="std-notes.html">Implementation Notes</a></span></dt></dl></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-susv5"></a>System interfaces compatible with the Single UNIX&#174; Specification Version 5:</h2></div></div></div><p>Note that the core of the Single UNIX&#174; Specification Version 5 is
POSIX&#174;.1-2024 also simultaneously IEEE Std 1003.1&#8482;-2024 Edition,
The &#174;Open Group Base Specifications Issue 8
(see https://pubs.opengroup.org/onlinepubs/9799919799/), and
ISO&#174;/IEC DIS 9945 Information technology
- Portable Operating System Interface (POSIX&#174;) base specifications
- Issue 8.</p><pre class="screen">
    CMPLX
    CMPLXF
    CMPLXL
    FD_CLR
    FD_ISSET
    FD_SET
    FD_ZERO
    _Exit
    _exit
    a64l
    abort
    abs
    accept
    accept4
    access
    acos
    acosf
    acosh
    acoshf
    acoshl
    acosl
    aio_cancel
    aio_error
    aio_fsync
    aio_read
    aio_return
    aio_suspend
    aio_write
    alarm
    aligned_alloc
    alphasort
    asctime
    asctime_r
    asin
    asinf
    asinh
    asinhf
    asinhl
    asinl
    asprintf
    assert
    at_quick_exit
    atan
    atan2
    atan2f
    atan2l
    atanf
    atanh
    atanhf
    atanhl
    atanl
    atexit
    atof
    atoi
    atol
    atoll
    atomic_compare_exchange_strong
    atomic_compare_exchange_strong_explicit
    atomic_compare_exchange_weak
    atomic_compare_exchange_weak_explicit
    atomic_exchange
    atomic_exchange_explicit
    atomic_fetch_add
    atomic_fetch_add_explicit
    atomic_fetch_and
    atomic_fetch_and_explicit
    atomic_fetch_or
    atomic_fetch_or_explicit
    atomic_fetch_sub
    atomic_fetch_sub_explicit
    atomic_fetch_xor
    atomic_fetch_xor_explicit
    atomic_flag_clear
    atomic_flag_clear_explicit
    atomic_flag_test_and_set
    atomic_flag_test_and_set_explicit
    atomic_init
    atomic_is_lock_free
    atomic_load
    atomic_load_explicit
    atomic_signal_fence
    atomic_store
    atomic_store_explicit
    atomic_thread_fence
    basename			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    be16toh
    be32toh
    be64toh
    bind
    bind_textdomain_codeset
    bindtextdomain
    bsearch
    btowc
    c16rtomb
    c32rtomb
    cabs
    cabsf
    cabsl
    cacos
    cacosf
    cacosh
    cacoshf
    cacoshl
    cacosl
    call_once
    calloc
    carg
    cargf
    cargl
    casin
    casinf
    casinh
    casinhf
    casinhl
    casinl
    catan
    catanf
    catanh
    catanhf
    catanhl
    catanl
    catclose
    catgets
    catopen
    cbrt
    cbrtf
    cbrtl
    ccos
    ccosf
    ccosh
    ccoshf
    ccoshl
    ccosl
    ceil
    ceilf
    ceill
    cexp
    cexpf
    cexpl
    cfgetispeed
    cfgetospeed
    cfsetispeed
    cfsetospeed
    chdir
    chmod
    chown
    cimag
    cimagf
    cimagl
    clearerr
    clock
    clock_getcpuclockid
    clock_getres
    clock_gettime
    clock_nanosleep		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    clock_settime		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    clog
    clogf
    clogl
    close
    closedir
    closelog
    cnd_broadcast
    cnd_destroy
    cnd_init
    cnd_signal
    cnd_timedwait
    cnd_wait
    confstr
    conj
    conjf
    conjl
    connect
    copysign
    copysignf
    copysignl
    cos
    cosf
    cosh
    coshf
    coshl
    cosl
    cpow
    cpowf
    cpowl
    cproj
    cprojf
    cprojl
    creal
    crealf
    creall
    creat
    crypt			(available in external "libcrypt" library)
    csin
    csinf
    csinh
    csinhf
    csinhl
    csinl
    csqrt
    csqrtf
    csqrtl
    ctan
    ctanf
    ctanh
    ctanhf
    ctanhl
    ctanl
    ctermid
    ctime
    ctime_r
    daylight
    dbm_clearerr		(available in external "libgdbm" library)
    dbm_close			(available in external "libgdbm" library)
    dbm_delete			(available in external "libgdbm" library)
    dbm_error			(available in external "libgdbm" library)
    dbm_fetch			(available in external "libgdbm" library)
    dbm_firstkey		(available in external "libgdbm" library)
    dbm_nextkey			(available in external "libgdbm" library)
    dbm_open			(available in external "libgdbm" library)
    dbm_store			(available in external "libgdbm" library)
    dcgettext			(available in external "libintl" library)
    dcngettext			(available in external "libintl" library)
    dgettext			(available in external "libintl" library)
    difftime
    dirfd
    dirname
    div
    dladdr			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    dlclose
    dlerror
    dlopen
    dlsym
    dngettext			(available in external "libintl" library)
    dprintf
    drand48
    dup
    dup2
    dup3
    duplocale
    encrypt			(available in external "libcrypt" library)
    endgrent
    endhostent
    endprotoent
    endpwent
    endservent
    endutxent
    environ
    erand48
    erf
    erfc
    erfcf
    erfcl
    erff
    erfl
    errno
    execl
    execle
    execlp
    execv
    execve
    execvp
    exit
    exp
    exp2
    exp2f
    exp2l
    expf
    expl
    expm1
    expm1f
    expm1l
    fabs
    fabsf
    fabsl
    faccessat
    fchdir
    fchmod
    fchmodat
    fchown
    fchownat
    fclose
    fcntl			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    fdatasync
    fdim
    fdimf
    fdiml
    fdopen
    fdopendir
    feclearexcept
    fegetenv
    fegetexceptflag
    fegetround
    feholdexcept
    feof
    feraiseexcept
    ferror
    fesetenv
    fesetexceptflag
    fesetround
    fetestexcept
    feupdateenv
    fexecve
    fflush
    ffs
    ffsl
    ffsll
    fgetc
    fgetpos
    fgets
    fgetwc
    fgetws
    fileno
    flockfile
    floor
    floorf
    floorl
    fma
    fmaf
    fmal
    fmax
    fmaxf
    fmaxl
    fmemopen
    fmin
    fminf
    fminl
    fmod
    fmodf
    fmodl
    fnmatch
    fopen
    fork
    fpathconf
    fpclassify			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    fprintf
    fputc
    fputs
    fputwc
    fputws
    fread
    free
    freeaddrinfo
    freelocale
    freopen
    frexp
    frexpf
    frexpl
    fscanf
    fseek
    fseeko
    fsetpos
    fstat
    fstatat
    fstatvfs
    fsync
    ftell
    ftello
    ftok
    ftruncate
    ftrylockfile
    funlockfile
    futimens
    fwide
    fwprintf
    fwrite
    fwscanf
    gai_strerror
    getaddrinfo
    getc
    getc_unlocked
    getchar
    getchar_unlocked
    getcwd
    getdelim
    getegid
    getentropy
    getenv
    geteuid
    getgid
    getgrent
    getgrgid
    getgrgid_r
    getgrnam
    getgrnam_r
    getgroups
    gethostid
    gethostname
    getline
    getlocalename_l
    getlogin
    getlogin_r
    getnameinfo
    getopt
    getpeername
    getpgid
    getpgrp
    getpid
    getppid
    getpriority
    getprotobyname
    getprotobynumber
    getprotoent
    getpwent
    getpwnam
    getpwnam_r
    getpwuid
    getpwuid_r
    getrlimit			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    getrusage
    getservbyname
    getservbyport
    getservent
    getsid
    getsockname
    getsockopt
    getsubopt
    gettext			(available in external "libintl" library)
    getuid
    getutxent
    getutxid
    getutxline
    getwc
    getwchar
    glob
    globfree
    gmtime
    gmtime_r
    grantpt
    hcreate
    hdestroy
    hsearch
    htobe16
    htobe32
    htobe64
    htole16
    htole32
    htole64
    htonl
    htons
    hypot
    hypotf
    hypotl
    iconv			(available in external "libiconv" library)
    iconv_close			(available in external "libiconv" library)
    iconv_open			(available in external "libiconv" library)
    if_freenameindex
    if_indextoname
    if_nameindex
    if_nametoindex
    ilogb
    ilogbf
    ilogbl
    imaxabs
    imaxdiv
    in6addr_any
    in6addr_loopback
    inet_addr
    inet_ntoa
    inet_ntop
    inet_pton
    initstate
    insque
    isalnum
    isalnum_l
    isalpha
    isalpha_l
    isatty
    isblank
    isblank_l
    iscntrl
    iscntrl_l
    isdigit
    isdigit_l
    isfinite			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isgraph
    isgraph_l
    isgreater			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isgreaterequal		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isinf			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isless
    islessequal			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    islessgreater		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    islower
    islower_l
    isnan			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isnormal			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isprint
    isprint_l
    ispunct
    ispunct_l
    isspace
    isspace_l
    isunordered			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    isupper
    isupper_l
    iswalnum
    iswalnum_l
    iswalpha
    iswalpha_l
    iswblank
    iswblank_l
    iswcntrl
    iswcntrl_l
    iswctype
    iswctype_l
    iswdigit
    iswdigit_l
    iswgraph
    iswgraph_l
    iswlower
    iswlower_l
    iswprint
    iswprint_l
    iswpunct
    iswpunct_l
    iswspace
    iswspace_l
    iswupper
    iswupper_l
    iswxdigit
    iswxdigit_l
    isxdigit
    isxdigit_l
    j0
    j1
    jn
    jrand48
    kill
    kill_dependency
    killpg
    l64a
    labs
    lchown
    lcong48
    ldexp
    ldexpf
    ldexpl
    ldiv
    le16toh
    le32toh
    le64toh
    lfind
    lgamma
    lgammaf
    lgammal
    link
    linkat
    lio_listio
    listen
    llabs
    lldiv
    llrint
    llrintf
    llrintl
    llround
    llroundf
    llroundl
    localeconv
    localtime
    localtime_r
    lockf			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    log
    log10
    log10f
    log10l
    log1p
    log1pf
    log1pl
    log2
    log2f
    log2l
    logb
    logbf
    logbl
    logf
    logl
    longjmp
    lrand48
    lrint
    lrintf
    lrintl
    lround
    lroundf
    lroundl
    lsearch
    lseek
    lstat
    malloc
    mblen
    mbrlen
    mbrtoc16
    mbrtoc32
    mbrtowc
    mbsinit
    mbsnrtowcs
    mbsrtowcs
    mbstowcs
    mbtowc
    memccpy
    memchr
    memcmp
    memcpy
    memmem
    memmove
    memset
    mkdir
    mkdirat
    mkdtemp
    mkfifo
    mkfifoat
    mknod
    mknodat
    mkostemp
    mkstemp
    mktime
    mlock
    mmap
    modf
    modff
    modfl
    mprotect
    mq_close
    mq_getattr
    mq_notify
    mq_open
    mq_receive
    mq_send
    mq_setattr
    mq_timedreceive
    mq_timedsend
    mq_unlink
    mrand48
    msgctl			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    msgget			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    msgrcv			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    msgsnd			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    msync
    mtx_destroy
    mtx_init
    mtx_lock
    mtx_timedlock
    mtx_trylock
    mtx_unlock
    munlock
    munmap
    nan
    nanf
    nanl
    nanosleep
    nearbyint
    nearbyintf
    nearbyintl
    newlocale
    nextafter
    nextafterf
    nextafterl
    nexttoward
    nexttowardf
    nexttowardl
    nftw
    ngettext			(available in external "libintl" library)
    nice			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    nl_langinfo
    nl_langinfo_l
    nrand48
    ntohl
    ntohs
    open
    open_memstream
    open_wmemstream
    openat
    opendir
    openlog
    optarg
    opterr
    optind
    optopt
    pathconf
    pause
    pclose
    perror
    pipe
    pipe2
    poll
    popen
    posix_close
    posix_devctl
    posix_fadvise
    posix_fallocate
    posix_getdents
    posix_madvise
    posix_memalign
    posix_openpt
    posix_spawn
    posix_spawn_file_actions_addchdir
    posix_spawn_file_actions_addclose
    posix_spawn_file_actions_adddup2
    posix_spawn_file_actions_addfchdir
    posix_spawn_file_actions_addopen
    posix_spawn_file_actions_destroy
    posix_spawn_file_actions_init
    posix_spawnattr_destroy
    posix_spawnattr_getflags
    posix_spawnattr_getpgroup
    posix_spawnattr_getschedparam
    posix_spawnattr_getschedpolicy
    posix_spawnattr_getsigdefault
    posix_spawnattr_getsigmask
    posix_spawnattr_init
    posix_spawnattr_setflags
    posix_spawnattr_setpgroup
    posix_spawnattr_setschedparam
    posix_spawnattr_setschedpolicy
    posix_spawnattr_setsigdefault
    posix_spawnattr_setsigmask
    posix_spawnp
    pow
    powf
    powl
    ppoll
    pread
    printf
    pselect
    psiginfo
    psignal
    pthread_atfork
    pthread_attr_destroy
    pthread_attr_getdetachstate
    pthread_attr_getguardsize
    pthread_attr_getinheritsched
    pthread_attr_getschedparam
    pthread_attr_getschedpolicy
    pthread_attr_getscope
    pthread_attr_getstack
    pthread_attr_getstacksize
    pthread_attr_init
    pthread_attr_setdetachstate
    pthread_attr_setguardsize
    pthread_attr_setinheritsched
    pthread_attr_setschedparam
    pthread_attr_setschedpolicy
    pthread_attr_setscope
    pthread_attr_setstack
    pthread_attr_setstacksize
    pthread_barrier_destroy
    pthread_barrier_init
    pthread_barrier_wait
    pthread_barrierattr_destroy
    pthread_barrierattr_getpshared
    pthread_barrierattr_init
    pthread_barrierattr_setpshared
    pthread_cancel
    pthread_cleanup_pop
    pthread_cleanup_push
    pthread_cond_broadcast
    pthread_cond_clockwait
    pthread_cond_destroy
    pthread_cond_init
    pthread_cond_signal
    pthread_cond_timedwait
    pthread_cond_wait
    pthread_condattr_destroy
    pthread_condattr_getclock
    pthread_condattr_getpshared
    pthread_condattr_init
    pthread_condattr_setclock
    pthread_condattr_setpshared
    pthread_create
    pthread_detach
    pthread_equal
    pthread_exit
    pthread_getcpuclockid
    pthread_getschedparam
    pthread_getspecific
    pthread_join
    pthread_key_create
    pthread_key_delete
    pthread_kill
    pthread_mutex_clocklock
    pthread_mutex_destroy
    pthread_mutex_getprioceiling
    pthread_mutex_init
    pthread_mutex_lock
    pthread_mutex_setprioceiling
    pthread_mutex_timedlock
    pthread_mutex_trylock
    pthread_mutex_unlock
    pthread_mutexattr_destroy
    pthread_mutexattr_getprioceiling
    pthread_mutexattr_getprotocol
    pthread_mutexattr_getpshared
    pthread_mutexattr_gettype
    pthread_mutexattr_init
    pthread_mutexattr_setprioceiling
    pthread_mutexattr_setprotocol
    pthread_mutexattr_setpshared
    pthread_mutexattr_settype
    pthread_once
    pthread_rwlock_clockrdlock
    pthread_rwlock_clockwrlock
    pthread_rwlock_destroy
    pthread_rwlock_init
    pthread_rwlock_rdlock
    pthread_rwlock_timedrdlock
    pthread_rwlock_timedwrlock
    pthread_rwlock_tryrdlock
    pthread_rwlock_trywrlock
    pthread_rwlock_unlock
    pthread_rwlock_wrlock
    pthread_rwlockattr_destroy
    pthread_rwlockattr_getpshared
    pthread_rwlockattr_init
    pthread_rwlockattr_setpshared
    pthread_self
    pthread_setcancelstate
    pthread_setcanceltype
    pthread_setschedparam
    pthread_setschedprio
    pthread_setspecific
    pthread_sigmask
    pthread_spin_destroy
    pthread_spin_init
    pthread_spin_lock
    pthread_spin_trylock
    pthread_spin_unlock
    pthread_testcancel
    ptsname
    ptsname_r
    putc
    putc_unlocked
    putchar
    putchar_unlocked
    putenv
    puts
    pututxline
    putwc
    putwchar
    pwrite
    qsort
    qsort_r			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    quick_exit
    raise
    rand
    random
    read
    readdir
    readdir_r
    readlink
    readlinkat
    readv
    realloc
    reallocarray
    realpath
    recv
    recvfrom
    recvmsg
    regcomp
    regerror
    regexec
    regfree
    remainder
    remainderf
    remainderl
    remove
    remque
    remquo
    remquof
    remquol
    rename
    renameat
    rewind
    rewinddir
    rint
    rintf
    rintl
    rmdir
    round
    roundf
    roundl
    scalbln
    scalblnf
    scalblnl
    scalbn
    scalbnf
    scalbnl
    scandir
    scanf
    sched_get_priority_max
    sched_get_priority_min
    sched_getparam
    sched_getscheduler
    sched_rr_get_interval
    sched_setparam		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    sched_setscheduler		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    sched_yield
    secure_getenv
    seed48
    seekdir
    select
    sem_clockwait
    sem_close
    sem_destroy
    sem_getvalue
    sem_init
    sem_open
    sem_post
    sem_timedwait
    sem_trywait
    sem_unlink
    sem_wait
    semctl			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    semget			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    semop			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    send
    sendmsg
    sendto
    setbuf
    setegid
    setenv
    seteuid
    setgid
    setgrent
    sethostent
    setjmp
    setkey			(available in external "libcrypt" library)
    setlocale
    setlogmask
    setpgid
    setpriority			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    setprotoent
    setpwent
    setregid
    setreuid
    setrlimit			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    setservent
    setsid
    setsockopt
    setstate
    setuid
    setutxent
    setvbuf
    shm_open
    shm_unlink
    shmat			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    shmctl			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    shmdt			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    shmget			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    shutdown
    sig2str
    sigaction
    sigaddset
    sigaltstack
    sigdelset
    sigemptyset
    sigfillset
    sigismember
    siglongjmp
    signal
    signbit			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    signgam
    sigpending
    sigprocmask
    sigqueue
    sigsetjmp
    sigsuspend
    sigtimedwait
    sigwait
    sigwaitinfo
    sin
    sinf
    sinh
    sinhf
    sinhl
    sinl
    sleep
    snprintf
    sockatmark
    socket
    socketpair
    sprintf
    sqrt
    sqrtf
    sqrtl
    srand
    srand48
    srandom
    sscanf
    stat
    statvfs
    stderr
    stdin
    stdout
    stpcpy
    stpncpy
    str2sig
    strcasecmp
    strcasecmp_l
    strcat
    strchr
    strcmp
    strcoll
    strcoll_l
    strcpy
    strcspn
    strdup
    strerror
    strerror_l
    strerror_r			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    strfmon
    strfmon_l
    strftime
    strftime_l
    strlcat
    strlcpy
    strlen
    strncasecmp
    strncasecmp_l
    strncat
    strncmp
    strncpy
    strndup
    strnlen
    strpbrk
    strptime
    strrchr
    strsignal
    strspn
    strstr
    strtod
    strtof
    strtoimax
    strtok
    strtok_r
    strtol
    strtold
    strtoll
    strtoul
    strtoull
    strtoumax
    strxfrm
    strxfrm_l
    swab
    swprintf
    swscanf
    symlink
    symlinkat
    sync
    sysconf
    syslog
    system
    tan
    tanf
    tanh
    tanhf
    tanhl
    tanl
    tcdrain
    tcflow
    tcflush
    tcgetattr
    tcgetpgrp
    tcgetsid
    tcgetwinsize
    tcsendbreak
    tcsetattr
    tcsetpgrp
    tcsetwinsize
    tdelete
    telldir
    textdomain			(available in external "libintl" library)
    tfind
    tgamma
    tgammaf
    tgammal
    thrd_create
    thrd_current
    thrd_detach
    thrd_equal
    thrd_exit
    thrd_join
    thrd_sleep
    thrd_yield
    time
    timer_create		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    timer_delete
    timer_getoverrun
    timer_gettime
    timer_settime
    times
    timespec_get
    timezone
    tmpfile
    tmpnam
    tolower
    tolower_l
    toupper
    toupper_l
    towctrans
    towctrans_l
    towlower
    towlower_l
    towupper
    towupper_l
    trunc
    truncate
    truncf
    truncl
    tsearch
    tss_create
    tss_delete
    tss_get
    tss_set
    ttyname
    ttyname_r
    twalk
    tzname
    tzset
    umask
    uname
    ungetc
    ungetwc
    unlink
    unlinkat
    unlockpt
    unsetenv
    uselocale
    utimensat
    utimes
    va_arg
    va_copy
    va_end
    va_start
    vasprintf
    vdprintf
    vfprintf
    vfscanf
    vfwprintf
    vfwscanf
    vprintf
    vscanf
    vsnprintf
    vsprintf
    vsscanf
    vswprintf
    vswscanf
    vwprintf
    vwscanf
    wait
    waitpid
    wcpcpy
    wcpncpy
    wcrtomb
    wcscasecmp
    wcscasecmp_l
    wcscat
    wcschr
    wcscmp
    wcscoll
    wcscoll_l
    wcscpy
    wcscspn
    wcsdup
    wcsftime
    wcslcat
    wcslcpy
    wcslen
    wcsncasecmp
    wcsncasecmp_l
    wcsncat
    wcsncmp
    wcsncpy
    wcsnlen
    wcsnrtombs
    wcspbrk
    wcsrchr
    wcsrtombs
    wcsspn
    wcsstr
    wcstod
    wcstof
    wcstoimax
    wcstok
    wcstol
    wcstold
    wcstoll
    wcstombs
    wcstoul
    wcstoull
    wcstoumax
    wcswidth
    wcsxfrm
    wcsxfrm_l
    wctob
    wctomb
    wctrans
    wctrans_l
    wctype
    wctype_l
    wcwidth
    wmemchr
    wmemcmp
    wmemcpy
    wmemmove
    wmemset
    wordexp
    wordfree
    wprintf
    write
    writev
    wscanf
    y0
    y1
    yn
</pre></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="cygwin-api.html">Prev</a>&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="std-bsd.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Cygwin API Reference&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;System interfaces compatible with BSD functions:</td></tr></table></div></body></html>
