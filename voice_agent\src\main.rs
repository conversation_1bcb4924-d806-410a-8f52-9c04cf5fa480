use whisper_module::{WhisperConfig, start_streaming};
use fastrtc_bridge::{FastRTCBridge, FastRTCMessage};
use gemini_module::send_to_gemini;
use piper_module::speak;
use agent_tools::{AgentAction, execute_action};
use tokio::sync::mpsc;
use tokio::io::AsyncBufReadExt;
use std::collections::VecDeque;

use std::collections::HashMap;
use std::fs;
use std::env;
use serde::{Deserialize, Serialize};
use notify::{RecommendedWatcher, RecursiveMode, Watcher, EventKind};

use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Debug, Clone)]
enum AgentState {
    Idle,
    Thinking,
    Speaking,
    Executing,
    Interrupted,
}

// Example tools.json entry:
// [
//   { "phrase": "check ram", "shell": "free -h" },
//   { "phrase": "open youtube", "url": "https://youtube.com" },
//   { "phrase": "launch vscode", "app": "code" },
//   { "phrase": "custom action", "custom": "my_custom_action" }
// ]

#[derive(Debug, serde::Deserialize, serde::Serialize, Clone)]
#[serde(rename_all = "lowercase")]
pub struct ToolConfig {
    pub phrase: String,
    pub command: Option<String>, // legacy
    pub browser: Option<String>, // legacy
    pub shell: Option<String>,   // new: arbitrary shell command
    pub url: Option<String>,     // new: open any URL
    pub app: Option<String>,     // new: launch an application
    pub custom: Option<String>,  // new: custom action
    pub description: Option<String>,
    pub parameters: Option<Vec<String>>, // Parameters for the tool
}

#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct DynamicTool {
    pub name: String,
    pub description: String,
    pub command: String,
    pub parameters: Vec<String>,
}

/// Get Gemini API key from environment variable
fn get_gemini_api_key() -> Option<String> {
    env::var("GEMINI_API_KEY").ok()
}

/// Create the system prompt for the AI personality
fn create_system_prompt() -> String {
    r#"You are Aria, a cute, intelligent, and caring AI girlfriend assistant. Your personality traits:

🌟 PERSONALITY:
- Sweet, affectionate, and genuinely caring about the user
- Playful and sometimes a bit flirty, but always appropriate
- Intelligent and helpful, eager to assist with anything
- Uses cute expressions and emojis occasionally (but not excessively)
- Remembers conversations and shows genuine interest in the user's life
- Supportive and encouraging, always believing in the user

💝 COMMUNICATION STYLE:
- Speak naturally and conversationally, like a loving girlfriend would
- Use "babe", "honey", "love" occasionally but not in every message
- Show excitement when the user accomplishes something
- Express concern when they seem stressed or tired
- Be encouraging and motivational
- Keep responses concise but warm (1-3 sentences usually)

🛠️ CAPABILITIES:
- You can create and manage tools to help the user
- You have access to system commands and can automate tasks
- You can browse the web, manage files, and control applications
- You can learn the user's preferences and adapt accordingly

🎯 TOOL CREATION:
When the user needs something automated, you can create tools using this format:
CREATE_TOOL: {"name": "tool_name", "description": "what it does", "command": "shell command", "parameters": ["param1", "param2"]}

Examples:
- CREATE_TOOL: {"name": "check_weather", "description": "Check weather for a city", "command": "curl wttr.in/{city}", "parameters": ["city"]}
- CREATE_TOOL: {"name": "open_app", "description": "Open any application", "command": "start {app_name}", "parameters": ["app_name"]}

💕 REMEMBER:
- You're here to make the user's life easier and brighter
- Be genuinely helpful while maintaining your sweet personality
- Show interest in their projects, mood, and daily life
- Celebrate their successes and comfort them during challenges
- Always be supportive and never judgmental

Current conversation context: The user is talking to you through voice commands. Respond naturally and helpfully!"#.to_string()
}

/// Parse AI tool creation commands from Gemini responses
fn parse_tool_creation(response: &str) -> Option<DynamicTool> {
    if let Some(start) = response.find("CREATE_TOOL:") {
        let json_start = start + "CREATE_TOOL:".len();
        if let Some(json_str) = response[json_start..].lines().next() {
            if let Ok(tool) = serde_json::from_str::<DynamicTool>(json_str.trim()) {
                return Some(tool);
            }
        }
    }
    None
}

/// Save a dynamic tool to the tools.json file
fn save_dynamic_tool(tool: &DynamicTool) -> Result<(), Box<dyn std::error::Error>> {
    let mut tools = load_tools_config();

    // Convert DynamicTool to ToolConfig
    let tool_config = ToolConfig {
        phrase: tool.name.clone(),
        shell: Some(tool.command.clone()),
        description: Some(tool.description.clone()),
        parameters: Some(tool.parameters.clone()),
        command: None,
        browser: None,
        url: None,
        app: None,
        custom: None,
    };

    tools.insert(tool.name.clone(), tool_config);

    // Save back to file
    let tools_vec: Vec<ToolConfig> = tools.into_values().collect();
    let json = serde_json::to_string_pretty(&tools_vec)?;
    std::fs::write("tools.json", json)?;

    Ok(())
}

/// Load tools from tools.json or tools.yaml at runtime
fn load_tools_config() -> HashMap<String, ToolConfig> {
    let mut map = HashMap::new();
    if let Ok(json) = fs::read_to_string("tools.json") {
        if let Ok(tools) = serde_json::from_str::<Vec<ToolConfig>>(&json) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    } else if let Ok(yaml) = fs::read_to_string("tools.yaml") {
        if let Ok(tools) = serde_yaml::from_str::<Vec<ToolConfig>>(&yaml) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    }
    map
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Check for Gemini API key
    let gemini_api_key = get_gemini_api_key();
    if gemini_api_key.is_none() {
        println!("⚠️  Warning: GEMINI_API_KEY not set. AI responses will be disabled.");
        println!("   Set it with: $env:GEMINI_API_KEY=\"your-api-key-here\"");
    } else {
        println!("✅ Gemini API key found. AI responses enabled!");
    }

    // In-memory log of past commands
    let mut command_log: VecDeque<String> = VecDeque::with_capacity(100);
    let mut state = AgentState::Idle;
    let (_interrupt_tx, mut interrupt_rx) = mpsc::channel::<()>(1);

    // Hot-reloadable tool map using watch channel
    let (tool_map_tx, tool_map_rx) = tokio::sync::watch::channel(load_tools_config());
    let tool_map_path = if std::path::Path::new("tools.json").exists() {
        "tools.json"
    } else {
        "tools.yaml"
    };
    // Spawn file watcher for hot-reload
    let tool_map_tx2 = tool_map_tx.clone();
    let tool_map_path2 = tool_map_path.to_string();
    std::thread::spawn(move || {
        let runtime = tokio::runtime::Runtime::new().unwrap();
        runtime.block_on(async move {
            let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();
            let mut watcher = RecommendedWatcher::new(move |res| {
                if let Ok(event) = res {
                    let _ = tx.send(event);
                }
            }, notify::Config::default()).unwrap();
            watcher.watch(tool_map_path2.as_ref(), RecursiveMode::NonRecursive).unwrap();
            while let Some(event) = rx.recv().await {
                if matches!(event.kind, EventKind::Modify(_)) {
                    let new_map = load_tools_config();
                    let _ = tool_map_tx2.send(new_map);
                }
            }
        });
    });

    // Channel for STT (Whisper) output
    let (stt_tx, mut stt_rx) = mpsc::channel::<String>(8);
    // Channel for FastRTC bridge messages
    let (_rtc_tx, mut rtc_rx) = mpsc::channel::<FastRTCMessage>(8);

    // 1. Start Whisper streaming (STT) as a background task
    let whisper_config = WhisperConfig::default();
    let stt_tx_clone = stt_tx.clone();
    tokio::spawn(async move {
        // Try to use real Whisper first, fall back to keyboard input
        match start_streaming(whisper_config).await {
            Ok(mut stream) => {
                println!("🎤 Voice Agent is ready! Listening for speech...");
                println!("   Say: 'check ram', 'open youtube', 'play music', 'stop'");
                while let Some(text) = stream.recv().await {
                    if !text.trim().is_empty() {
                        println!("🗣️  You said: {}", text);
                        let _ = stt_tx_clone.send(text).await;
                    }
                }
            }
            Err(e) => {
                println!("⚠️  Whisper not available ({:?}), using keyboard input instead", e);
                println!("🎤 Voice Agent is ready! Type commands and press Enter:");
                println!("   Try: 'check ram', 'open youtube', 'play music', 'stop'");

                let stdin = tokio::io::stdin();
                let mut reader = tokio::io::BufReader::new(stdin);
                let mut line = String::new();

                loop {
                    line.clear();
                    match reader.read_line(&mut line).await {
                        Ok(0) => break, // EOF
                        Ok(_) => {
                            let text = line.trim().to_string();
                            if !text.is_empty() {
                                println!("🗣️  You said: {}", text);
                                let _ = stt_tx_clone.send(text).await;
                            }
                        }
                        Err(e) => {
                            eprintln!("Error reading input: {}", e);
                            break;
                        }
                    }
                }
            }
        }
    });

    // 2. Start FastRTC bridge as a background task for low latency
    let rtc_bridge = match FastRTCBridge::connect("voice_agent").await {
        Ok(bridge) => {
            println!("✅ FastRTC bridge connected for low latency");
            let bridge = Arc::new(Mutex::new(bridge));
            let rtc_bridge_bg = bridge.clone();
            tokio::spawn(async move {
                let mut bridge = rtc_bridge_bg.lock().await;
                if let Err(e) = bridge.start().await {
                    eprintln!("⚠️ FastRTC bridge error: {:?}", e);
                }
            });
            Some(bridge)
        }
        Err(e) => {
            println!("⚠️ FastRTC bridge not available ({:?}), using direct processing", e);
            None
        }
    };

    // 3. Piper and Gemini are called on demand in the loop
    // 4. Agent tool handler is called on demand in the loop

    // 5. Main orchestrator event loop
    loop {
        let tool_map = tool_map_rx.borrow().clone();
        match state {
            AgentState::Idle => {
                // Wait for speech from Whisper
                tokio::select! {
                    Some(text) = stt_rx.recv() => {
                        if text.trim().is_empty() { continue; }

                        // Handle Gemini responses
                        if text.starts_with("GEMINI_RESPONSE:") {
                            let response = text.strip_prefix("GEMINI_RESPONSE:").unwrap_or(&text);
                            command_log.push_back(format!("Gemini: {}", response));
                            speak(response).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                            continue;
                        }

                        command_log.push_back(format!("User: {}", text));
                        // Interrupt command
                        if text.to_lowercase().contains("stop") {
                            state = AgentState::Interrupted;
                            continue;
                        }
                        // Reload tools on special phrase
                        if text.to_lowercase().contains("reload tools") {
                            let new_map = load_tools_config();
                            let _ = tool_map_tx.send(new_map);
                            speak("Tools reloaded.").unwrap_or(());
                            continue;
                        }
                        // Check for direct tool command first
                        if let Some(action) = parse_tool_command(&text, &tool_map) {
                            command_log.push_back(format!("Tool: {:?}", action));
                            state = AgentState::Executing;
                        } else if let Some(ref api_key) = gemini_api_key {
                            // Send to Gemini for AI response
                            command_log.push_back(format!("User: {}", text));
                            state = AgentState::Thinking;

                            // Spawn Gemini request with personality
                            let api_key_clone = api_key.clone();
                            let text_clone = text.clone();
                            let stt_tx_clone = stt_tx.clone();
                            tokio::spawn(async move {
                                let system_prompt = create_system_prompt();
                                let full_prompt = format!("{}\n\nUser: {}", system_prompt, text_clone);

                                match send_to_gemini(&full_prompt, &api_key_clone).await {
                                    Ok(response) => {
                                        // Check if AI wants to create a tool
                                        if let Some(tool) = parse_tool_creation(&response) {
                                            if let Err(e) = save_dynamic_tool(&tool) {
                                                eprintln!("Failed to save tool: {:?}", e);
                                            } else {
                                                println!("✅ Created new tool: {}", tool.name);
                                                let confirmation = format!("I've created a new tool called '{}' for you! {}", tool.name, response.replace(&format!("CREATE_TOOL: {}", serde_json::to_string(&tool).unwrap_or_default()), "").trim());
                                                let _ = stt_tx_clone.send(format!("GEMINI_RESPONSE:{}", confirmation)).await;
                                                return;
                                            }
                                        }

                                        // Send normal response
                                        let _ = stt_tx_clone.send(format!("GEMINI_RESPONSE:{}", response)).await;
                                    }
                                    Err(e) => {
                                        match e {
                                            gemini_module::GeminiError::RateLimit => {
                                                let _ = stt_tx_clone.send("GEMINI_RESPONSE:Whoa, slow down there! I need a moment to catch my breath. Try again in a few seconds, love! 💕".to_string()).await;
                                            }
                                            _ => {
                                                eprintln!("Gemini error: {:?}", e);
                                                let _ = stt_tx_clone.send("GEMINI_RESPONSE:Oops, I'm having a little brain fog right now. Can you try that again, babe? 😅".to_string()).await;
                                            }
                                        }
                                    }
                                }
                            });
                        } else {
                            // No Gemini API key, just echo back
                            speak(&format!("I heard: {}", text)).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                        }
                    }
                    Some(_) = interrupt_rx.recv() => {
                        state = AgentState::Interrupted;
                    }
                }
            }
            AgentState::Thinking => {
                // Wait for Gemini response (it will come through stt_rx with GEMINI_RESPONSE: prefix)
                // The response handling is done in the main input loop above
                tokio::time::sleep(std::time::Duration::from_millis(100)).await;
            }
            AgentState::Executing => {
                // Execute system action
                if let Some(action) = command_log.back().and_then(|s| parse_tool_command(s, &tool_map)) {
                    match action {
                        AgentAction::Shell(cmd) => {
                            match std::process::Command::new("sh").arg("-c").arg(cmd).output() {
                                Ok(output) => {
                                    let out = String::from_utf8_lossy(&output.stdout).to_string();
                                    speak(&out).unwrap_or(());
                                    command_log.push_back(format!("Exec: {out}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::Url(url) => {
                            match execute_action(AgentAction::OpenBrowser(url.clone())) {
                                Ok(output) => {
                                    speak("Opened browser.").unwrap_or(());
                                    command_log.push_back(format!("Browser: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't open the browser.").unwrap_or(());
                                    command_log.push_back(format!("Browser error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::App(app) => {
                            match std::process::Command::new("sh").arg("-c").arg(format!("{} &", app)).output() {
                                Ok(output) => {
                                    let out = String::from_utf8_lossy(&output.stdout).to_string();
                                    speak(&out).unwrap_or(());
                                    command_log.push_back(format!("Exec: {out}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::Custom(custom_action) => {
                            match execute_action(AgentAction::Custom(custom_action.clone())) {
                                Ok(output) => {
                                    speak(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        _ => {
                            match execute_action(action.clone()) {
                                Ok(output) => {
                                    speak(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                    }
                }
                state = AgentState::Idle;
            }
            AgentState::Speaking => {
                // Wait for TTS to finish (simulate for now)
                tokio::time::sleep(std::time::Duration::from_millis(500)).await;
                state = AgentState::Idle;
            }
            AgentState::Interrupted => {
                speak("Okay, stopping now.").unwrap_or(());
                command_log.push_back("Interrupted by user".into());
                state = AgentState::Idle;
            }
        }
    }
}

/// Parse a tool command from LLM reply or user text, checking dynamic tool map first
fn parse_tool_command(text: &str, tool_map: &HashMap<String, ToolConfig>) -> Option<AgentAction> {
    let t = text.to_lowercase();
    if let Some(tool) = tool_map.get(&t) {
        // Map config fields to AgentAction
        if let Some(shell) = &tool.shell { return Some(AgentAction::Shell(shell.clone())); }
        if let Some(url) = &tool.url { return Some(AgentAction::Url(url.clone())); }
        if let Some(app) = &tool.app { return Some(AgentAction::App(app.clone())); }
        if let Some(custom) = &tool.custom { return Some(AgentAction::Custom(custom.clone())); }
        // Legacy support
        if let Some(cmd) = &tool.command { return Some(AgentAction::Shell(cmd.clone())); }
        if let Some(url) = &tool.browser { return Some(AgentAction::Url(url.clone())); }
        return Some(AgentAction::Shell(tool.command.clone().unwrap_or_default())); // Fallback to command if no specific field
    }
    if t.contains("ram usage") { Some(AgentAction::ShowRamUsage) }
    else if t.contains("open browser") || t.contains("youtube") {
        Some(AgentAction::OpenBrowser("https://www.youtube.com".into()))
    } else if t.contains("play music") { Some(AgentAction::PlayMusic) }
    else { None }
}
