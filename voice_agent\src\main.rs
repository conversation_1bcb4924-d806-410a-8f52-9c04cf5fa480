use whisper_module::{WhisperConfig, start_streaming};
use fastrtc_bridge::{FastRTCBridge, FastRTCMessage};

use piper_module::speak;
use agent_tools::{AgentAction, execute_action};
use tokio::sync::mpsc;
use std::collections::VecDeque;

use std::collections::HashMap;
use std::fs;
use notify::{RecommendedWatcher, RecursiveMode, Watcher, EventKind};

use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Debug, Clone)]
enum AgentState {
    Idle,
    Thinking,
    Speaking,
    Executing,
    Interrupted,
}

// Example tools.json entry:
// [
//   { "phrase": "check ram", "shell": "free -h" },
//   { "phrase": "open youtube", "url": "https://youtube.com" },
//   { "phrase": "launch vscode", "app": "code" },
//   { "phrase": "custom action", "custom": "my_custom_action" }
// ]

#[derive(Debug, serde::Deserialize, Clone)]
#[serde(rename_all = "lowercase")]
pub struct ToolConfig {
    pub phrase: String,
    pub command: Option<String>, // legacy
    pub browser: Option<String>, // legacy
    pub shell: Option<String>,   // new: arbitrary shell command
    pub url: Option<String>,     // new: open any URL
    pub app: Option<String>,     // new: launch an application
    pub custom: Option<String>,  // new: custom action
    pub description: Option<String>,
}

/// Load tools from tools.json or tools.yaml at runtime
fn load_tools_config() -> HashMap<String, ToolConfig> {
    let mut map = HashMap::new();
    if let Ok(json) = fs::read_to_string("tools.json") {
        if let Ok(tools) = serde_json::from_str::<Vec<ToolConfig>>(&json) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    } else if let Ok(yaml) = fs::read_to_string("tools.yaml") {
        if let Ok(tools) = serde_yaml::from_str::<Vec<ToolConfig>>(&yaml) {
            for tool in tools {
                map.insert(tool.phrase.to_lowercase(), tool);
            }
        }
    }
    map
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // In-memory log of past commands
    let mut command_log: VecDeque<String> = VecDeque::with_capacity(100);
    let mut state = AgentState::Idle;
    let (_interrupt_tx, mut interrupt_rx) = mpsc::channel::<()>(1);

    // Hot-reloadable tool map using watch channel
    let (tool_map_tx, tool_map_rx) = tokio::sync::watch::channel(load_tools_config());
    let tool_map_path = if std::path::Path::new("tools.json").exists() {
        "tools.json"
    } else {
        "tools.yaml"
    };
    // Spawn file watcher for hot-reload
    let tool_map_tx2 = tool_map_tx.clone();
    let tool_map_path2 = tool_map_path.to_string();
    std::thread::spawn(move || {
        let runtime = tokio::runtime::Runtime::new().unwrap();
        runtime.block_on(async move {
            let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();
            let mut watcher = RecommendedWatcher::new(move |res| {
                if let Ok(event) = res {
                    let _ = tx.send(event);
                }
            }, notify::Config::default()).unwrap();
            watcher.watch(tool_map_path2.as_ref(), RecursiveMode::NonRecursive).unwrap();
            while let Some(event) = rx.recv().await {
                if matches!(event.kind, EventKind::Modify(_)) {
                    let new_map = load_tools_config();
                    let _ = tool_map_tx2.send(new_map);
                }
            }
        });
    });

    // Channel for STT (Whisper) output
    let (stt_tx, mut stt_rx) = mpsc::channel::<String>(8);
    // Channel for FastRTC bridge messages
    let (_rtc_tx, mut rtc_rx) = mpsc::channel::<FastRTCMessage>(8);

    // 1. Start Whisper streaming (STT) as a background task
    let whisper_config = WhisperConfig::default();
    tokio::spawn(async move {
        let mut stream = start_streaming(whisper_config).await.unwrap();
        while let Some(text) = stream.recv().await {
            let _ = stt_tx.send(text).await;
        }
    });

    // 2. Start FastRTC bridge as a background task
    let rtc_bridge = Arc::new(Mutex::new(FastRTCBridge::connect("agent").await?));
    let rtc_bridge_bg = rtc_bridge.clone();
    tokio::spawn(async move {
        let mut bridge = rtc_bridge_bg.lock().await;
        bridge.start().await.unwrap();
    });

    // 3. Piper and Gemini are called on demand in the loop
    // 4. Agent tool handler is called on demand in the loop

    // 5. Main orchestrator event loop
    loop {
        let tool_map = tool_map_rx.borrow().clone();
        match state {
            AgentState::Idle => {
                // Wait for speech from Whisper
                tokio::select! {
                    Some(text) = stt_rx.recv() => {
                        if text.trim().is_empty() { continue; }
                        command_log.push_back(format!("User: {}", text));
                        // Interrupt command
                        if text.to_lowercase().contains("stop") {
                            state = AgentState::Interrupted;
                            continue;
                        }
                        // Reload tools on special phrase
                        if text.to_lowercase().contains("reload tools") {
                            let new_map = load_tools_config();
                            let _ = tool_map_tx.send(new_map);
                            speak("Tools reloaded.").unwrap_or(());
                            continue;
                        }
                        // Pass to RTC bridge
                        let msg = FastRTCMessage { r#type: "send_input".into(), data: text.clone() };
                        let bridge = rtc_bridge.lock().await;
                        bridge.send_text(msg).await?;
                        state = AgentState::Thinking;
                    }
                    Some(_) = interrupt_rx.recv() => {
                        state = AgentState::Interrupted;
                    }
                }
            }
            AgentState::Thinking => {
                // Wait for LLM reply via RTC or direct
                if let Some(msg) = rtc_rx.recv().await {
                    if msg.r#type == "llm_reply" {
                        command_log.push_back(format!("Gemini: {}", msg.data));
                        // Check for tool command
                        if let Some(action) = parse_tool_command(&msg.data, &tool_map) {
                            state = AgentState::Executing;
                            // Save action for next loop
                            command_log.push_back(format!("Tool: {:?}", action));
                        } else {
                            // Speak reply
                            speak(&msg.data).unwrap_or_else(|e| eprintln!("TTS error: {e:?}"));
                            state = AgentState::Speaking;
                        }
                    } else if msg.r#type == "error" {
                        command_log.push_back(format!("Error: {}", msg.data));
                        speak("Sorry, I had a problem thinking.").unwrap_or(());
                        state = AgentState::Idle;
                    }
                }
            }
            AgentState::Executing => {
                // Execute system action
                if let Some(action) = command_log.back().and_then(|s| parse_tool_command(s, &tool_map)) {
                    match action {
                        AgentAction::Shell(cmd) => {
                            match std::process::Command::new("sh").arg("-c").arg(cmd).output() {
                                Ok(output) => {
                                    let out = String::from_utf8_lossy(&output.stdout).to_string();
                                    speak(&out).unwrap_or(());
                                    command_log.push_back(format!("Exec: {out}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::Url(url) => {
                            match execute_action(AgentAction::OpenBrowser(url.clone())) {
                                Ok(output) => {
                                    speak("Opened browser.").unwrap_or(());
                                    command_log.push_back(format!("Browser: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't open the browser.").unwrap_or(());
                                    command_log.push_back(format!("Browser error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::App(app) => {
                            match std::process::Command::new("sh").arg("-c").arg(format!("{} &", app)).output() {
                                Ok(output) => {
                                    let out = String::from_utf8_lossy(&output.stdout).to_string();
                                    speak(&out).unwrap_or(());
                                    command_log.push_back(format!("Exec: {out}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        AgentAction::Custom(custom_action) => {
                            match execute_action(AgentAction::Custom(custom_action.clone())) {
                                Ok(output) => {
                                    speak(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                        _ => {
                            match execute_action(action.clone()) {
                                Ok(output) => {
                                    speak(&output).unwrap_or(());
                                    command_log.push_back(format!("Exec: {output}"));
                                }
                                Err(e) => {
                                    speak("Sorry, I couldn't execute that.").unwrap_or(());
                                    command_log.push_back(format!("Exec error: {e:?}"));
                                }
                            }
                        }
                    }
                }
                state = AgentState::Idle;
            }
            AgentState::Speaking => {
                // Wait for TTS to finish (simulate for now)
                tokio::time::sleep(std::time::Duration::from_millis(500)).await;
                state = AgentState::Idle;
            }
            AgentState::Interrupted => {
                speak("Okay, stopping now.").unwrap_or(());
                command_log.push_back("Interrupted by user".into());
                state = AgentState::Idle;
            }
        }
    }
}

/// Parse a tool command from LLM reply or user text, checking dynamic tool map first
fn parse_tool_command(text: &str, tool_map: &HashMap<String, ToolConfig>) -> Option<AgentAction> {
    let t = text.to_lowercase();
    if let Some(tool) = tool_map.get(&t) {
        // Map config fields to AgentAction
        if let Some(shell) = &tool.shell { return Some(AgentAction::Shell(shell.clone())); }
        if let Some(url) = &tool.url { return Some(AgentAction::Url(url.clone())); }
        if let Some(app) = &tool.app { return Some(AgentAction::App(app.clone())); }
        if let Some(custom) = &tool.custom { return Some(AgentAction::Custom(custom.clone())); }
        // Legacy support
        if let Some(cmd) = &tool.command { return Some(AgentAction::Shell(cmd.clone())); }
        if let Some(url) = &tool.browser { return Some(AgentAction::Url(url.clone())); }
        return Some(AgentAction::Shell(tool.command.clone().unwrap_or_default())); // Fallback to command if no specific field
    }
    if t.contains("ram usage") { Some(AgentAction::ShowRamUsage) }
    else if t.contains("open browser") || t.contains("youtube") {
        Some(AgentAction::OpenBrowser("https://www.youtube.com".into()))
    } else if t.contains("play music") { Some(AgentAction::PlayMusic) }
    else { None }
}
