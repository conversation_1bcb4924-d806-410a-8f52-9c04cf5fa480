<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_set_impersonation_token</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="func-cygwin-login.html" title="Helper functions to change user context"><link rel="prev" href="func-cygwin-logon_user.html" title="cygwin_logon_user"><link rel="next" href="func-cygwin-misc.html" title="Miscellaneous functions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_set_impersonation_token</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-logon_user.html">Prev</a>&#160;</td><th width="60%" align="center">Helper functions to change user context</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-misc.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-set-impersonation-token"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_set_impersonation_token</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">void
<b class="fsfunc">cygwin_set_impersonation_token</b>(</code>const HANDLE <var class="pdparam">token</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-set-impersonation-token-desc"></a><h2>Description</h2><p>Use this function to enable the token given as parameter as
impersonation token for the next call to <code class="function">setuid</code> or
<code class="function">seteuid</code>.  Use
<code class="function">cygwin_set_impersonation_token</code> together with
<code class="function">cygwin_logon_user</code> to impersonate users using
password authentication.</p></div><div class="refsect1"><a name="func-cygwin-set-impersonation-token-also"></a><h2>See also</h2><p>See also the chapter
<a class="ulink" href="../cygwin-ug-net/ntsec.html#ntsec-setuid-overview" target="_top">Switching the user context</a>
in the Cygwin User's guide.</p><p>See also <a class="link" href="func-cygwin-logon_user.html" title="cygwin_logon_user">cygwin_logon_user</a></p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-logon_user.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="func-cygwin-login.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-misc.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_logon_user&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Miscellaneous functions</td></tr></table></div></body></html>
