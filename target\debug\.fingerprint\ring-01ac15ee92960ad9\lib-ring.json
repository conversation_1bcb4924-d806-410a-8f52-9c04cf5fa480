{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 2241668132362809309, "path": 9543768005742346670, "deps": [[2317793503723491507, "untrusted", false, 14745165233722582494], [3016319839805820069, "build_script_build", false, 10518675091992026535], [9009208741846480474, "spin", false, 338542604128963977], [10020888071089587331, "<PERSON>ap<PERSON>", false, 9774706859059315658]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-01ac15ee92960ad9\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}