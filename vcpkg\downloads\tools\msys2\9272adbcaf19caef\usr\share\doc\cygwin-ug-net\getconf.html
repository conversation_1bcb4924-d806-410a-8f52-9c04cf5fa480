<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>getconf</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="dumper.html" title="dumper"><link rel="next" href="getfacl.html" title="getfacl"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">getconf</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="dumper.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="getfacl.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="getconf"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>getconf &#8212; Get configuration values</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">getconf</code>  [-v <em class="replaceable"><code>specification</code></em>]  <em class="replaceable"><code>variable_name</code></em>  [<em class="replaceable"><code>pathname</code></em>]</p></div><div class="cmdsynopsis"><p><code class="command">getconf</code>   -a  [<em class="replaceable"><code>pathname</code></em>]</p></div><div class="cmdsynopsis"><p><code class="command">getconf</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="getconf-options"></a><h2>Options</h2><pre class="screen">
  -v specification     Indicate specific version for which configuration
                       values shall be fetched.
  -a, --all            Print all known configuration values

Other options:

  -h, --help           This text
  -V, --version        Print program version and exit
</pre></div><div class="refsect1"><a name="getconf-desc"></a><h2>Description</h2><p>The <span class="command"><strong>getconf</strong></span> utility prints the value of the
      configuration variable specified by <code class="literal">variable_name</code>. If
      no <code class="literal">pathname</code> is given, <span class="command"><strong>getconf</strong></span>
      serves as a wrapper for the <code class="literal">confstr</code> and
      <code class="literal">sysconf</code> functions, supporting the symbolic constants
      defined in the <code class="literal">limits.h</code> and
      <code class="literal">unistd.h</code> headers, without their respective
      <code class="literal">_CS_</code> or <code class="literal">_SC_</code> prefixes. </p><p>If <code class="literal">pathname</code> is given, <span class="command"><strong>getconf</strong></span>
      prints the value of the configuration variable for the specified
      pathname. In this form, <span class="command"><strong>getconf</strong></span> serves as a wrapper
      for the <code class="literal">pathconf</code> function, supporting the symbolic
      constants defined in the <code class="literal">unistd.h</code> header, without the
      <code class="literal">_PC_</code> prefix. </p><p>If you specify the <code class="literal">-v</code> option, the parameter
      denotes a specification for which the value of the configuration variable
      should be printed. Note that the only specifications supported by Cygwin
      are <code class="literal">POSIX_V7_ILP32_OFFBIG</code> and the legacy
      <code class="literal">POSIX_V6_ILP32_OFFBIG</code> and
      <code class="literal">XBS5_ILP32_OFFBIG</code> equivalents.</p><p>Use the <code class="literal">-a</code> option to print a list of all available
      configuration variables for the system, or given
      <code class="literal">pathname</code>, and their values.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="dumper.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="getfacl.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">dumper&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;getfacl</td></tr></table></div></body></html>
