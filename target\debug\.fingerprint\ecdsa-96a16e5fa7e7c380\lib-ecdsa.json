{"rustc": 1842507548689473721, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 15657897354478470176, "path": 17760564894305177630, "deps": [[4234225094004207019, "rfc6979", false, 9931565059091784968], [10149501514950982522, "elliptic_curve", false, 9310437510034548168], [10800937535932116261, "der", false, 10989839674620568243], [11285023886693207100, "spki", false, 13899487110310225213], [13895928991373641935, "signature", false, 10575550409248009299], [17475753849556516473, "digest", false, 15104187478864408736]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ecdsa-96a16e5fa7e7c380\\dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}