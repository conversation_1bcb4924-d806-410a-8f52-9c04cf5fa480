{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 2485130472329912235, "deps": [[5103565458935487, "futures_io", false, 17343819074470059527], [1615478164327904835, "pin_utils", false, 4190866484279971785], [1811549171721445101, "futures_channel", false, 14243427294437473204], [1906322745568073236, "pin_project_lite", false, 7915125176091775041], [5451793922601807560, "slab", false, 15332114594728123701], [7013762810557009322, "futures_sink", false, 8335408254793702234], [7620660491849607393, "futures_core", false, 13296020411631890703], [10565019901765856648, "futures_macro", false, 12880741131194560044], [15932120279885307830, "memchr", false, 3454513404922105438], [16240732885093539806, "futures_task", false, 14683701586217419152]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-9d6bce3061e36dd9\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}