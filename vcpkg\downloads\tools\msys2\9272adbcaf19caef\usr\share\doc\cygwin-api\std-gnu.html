<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>System interfaces compatible with GNU or Linux extensions:</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="prev" href="std-bsd.html" title="System interfaces compatible with BSD functions:"><link rel="next" href="std-solaris.html" title="System interfaces compatible with Solaris or SunOS functions:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">System interfaces compatible with GNU or Linux extensions:</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="std-bsd.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Compatibility</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-solaris.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-gnu"></a>System interfaces compatible with GNU or Linux extensions:</h2></div></div></div><pre class="screen">
    __mempcpy
    argz_add
    argz_add_sep
    argz_append
    argz_count
    argz_create
    argz_create_sep
    argz_delete
    argz_extract
    argz_insert
    argz_next
    argz_replace
    argz_stringify
    asnprintf
    asprintf_r
    basename			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    canonicalize_file_name
    clearenv
    clog10
    clog10f
    clog10l
    close_range			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    crypt_r			(available in external "libcrypt" library)
    dremf
    envz_add
    envz_entry
    envz_get
    envz_merge
    envz_remove
    envz_strip
    error
    error_at_line
    euidaccess
    execvpe
    exp10
    exp10f
    exp10l
    fallocate			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    fcloseall
    fcloseall_r
    fedisableexcept
    feenableexcept
    fegetexcept
    fgets_unlocked
    fgetwc_unlocked
    fgetws_unlocked
    fgetxattr
    flistxattr
    fopencookie
    fputs_unlocked
    fputwc_unlocked
    fputws_unlocked
    fremovexattr
    fsetxattr
    get_avphys_pages
    get_current_dir_name
    get_nprocs
    get_nprocs_conf
    get_phys_pages
    getmntent_r
    getopt_long
    getopt_long_only
    getpt
    getwc_unlocked
    getwchar_unlocked
    getxattr
    lgetxattr
    listxattr
    llistxattr
    lremovexattr
    lsetxattr
    mempcpy
    memrchr
    mkostemps
    posix_spawn_file_actions_addchdir_np
    posix_spawn_file_actions_addfchdir_np
    pow10
    pow10f
    pow10l
    pthread_getaffinity_np
    pthread_getattr_np
    pthread_getname_np
    pthread_setaffinity_np
    pthread_setname_np
    pthread_sigqueue
    pthread_timedjoin_np
    pthread_tryjoin_np
    putwc_unlocked
    putwchar_unlocked
    quotactl
    rawmemchr
    removexattr
    renameat2			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    scandirat
    sched_getaffinity
    sched_getcpu
    sched_setaffinity
    setxattr
    signalfd
    sincos
    sincosf
    sincosl
    strchrnul
    strptime_l
    strtod_l
    strtof_l
    strtol_l
    strtold_l
    strtoll_l
    strtoul_l
    strtoull_l
    strverscmp
    sysinfo
    tdestroy
    timegm
    timelocal
    timerfd_create
    timerfd_gettime
    timerfd_settime
    toascii_l
    updwtmpx
    utmpxname
    vasnprintf
    vasprintf_r
    versionsort
    wcsftime_l
    wcstod_l
    wcstof_l
    wcstol_l
    wcstold_l
    wcstoll_l
    wcstoul_l
    wcstoull_l
    wmempcpy
</pre></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="std-bsd.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="compatibility.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="std-solaris.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">System interfaces compatible with BSD functions:&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;System interfaces compatible with Solaris or SunOS functions:</td></tr></table></div></body></html>
