'\" t
.\"     Title: getfacl
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "GETFACL" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
getfacl \- Display file and directory access control lists (ACLs)
.SH "SYNOPSIS"
.HP \w'\fBgetfacl\fR\ 'u
\fBgetfacl\fR [\-adceEn] \fIFILE\fR...
.HP \w'\fBgetfacl\fR\ 'u
\fBgetfacl\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
  \-a, \-\-access        display the file access control list only
  \-d, \-\-default       display the default access control list only
  \-c, \-\-omit\-header   do not display the comment header
  \-e, \-\-all\-effective print all effective rights
  \-E, \-\-no\-effective  print no effective rights
  \-n, \-\-numeric       print numeric user/group identifiers
  \-V, \-\-version       print version and exit
  \-h, \-\-help          this help text

When multiple files are specified on the command line, a blank
line separates the ACLs for each file\&.
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
For each argument that is a regular file, special file or directory,
\fBgetfacl\fR
displays the owner, the group, and the ACL\&. For directories
\fBgetfacl\fR
displays additionally the default ACL\&. With no options specified,
\fBgetfacl\fR
displays the filename, the owner, the group, the setuid (s), setgid (s), and sticky (t) bits if available, and both the ACL and the default ACL, if it exists\&. For more information on Cygwin and Windows ACLs, see
the section called \(lqPOSIX accounts, permission, and security\(rq
in the Cygwin User\*(Aqs Guide\&. The format for ACL output is as follows:
.sp
.if n \{\
.RS 4
.\}
.nf
     # file: filename
     # owner: name or uid
     # group: name or uid
     # flags: sst
     user::perm
     user:name or uid:perm
     group::perm
     group:name or gid:perm
     mask:perm
     other:perm
     default:user::perm
     default:user:name or uid:perm
     default:group::perm
     default:group:name or gid:perm
     default:mask:perm
     default:other:perm
.fi
.if n \{\
.RE
.\}
.sp
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
