use std::path::PathBuf;
use std::process::Stdio;
use tokio::process::Command as TokioCommand;
use tokio::sync::mpsc::{self, Receiver};
use tokio::io::{AsyncBufReadExt, BufReader as AsyncBufReader};
use tokio::task;

#[derive(Debug, <PERSON>lone)]
pub struct WhisperConfig {
    pub model_path: PathBuf,
    pub step_ms: u32,      // e.g. 500
    pub length_ms: u32,    // e.g. 5000
    pub threads: u32,      // e.g. 4
    pub language: String,  // e.g. "en"
    pub whisper_bin: PathBuf, // path to whisper-stream binary
}

impl Default for WhisperConfig {
    fn default() -> Self {
        Self {
            model_path: PathBuf::from("models/ggml-base.en.bin"),
            step_ms: 500,
            length_ms: 5000,
            threads: 4,
            language: "en".to_string(),
            whisper_bin: PathBuf::from("whisper.cpp/build/bin/Release/whisper-cli.exe"),
        }
    }
}

#[derive(Debug)]
pub enum WhisperError {
    Io(std::io::Error),
    Process(String),
    StreamNotFound,
}

impl From<std::io::Error> for WhisperError {
    fn from(e: std::io::Error) -> Self {
        WhisperError::Io(e)
    }
}

/// Starts whisper-stream and returns a channel receiver for real-time transcriptions.
pub async fn start_streaming(config: WhisperConfig) -> Result<Receiver<String>, WhisperError> {
    // Check if binary exists
    if !config.whisper_bin.exists() {
        return Err(WhisperError::StreamNotFound);
    }
    // Build command
    let mut cmd = TokioCommand::new(&config.whisper_bin);
    cmd.arg("-m").arg(&config.model_path)
        .arg("-t").arg(config.threads.to_string())
        .arg("--step").arg(config.step_ms.to_string())
        .arg("--length").arg(config.length_ms.to_string())
        .arg("-l").arg(&config.language)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());
    let mut child = cmd.spawn()?;
    let stdout = child.stdout.take().ok_or_else(|| WhisperError::Process("Failed to capture stdout".to_string()))?;
    let reader = AsyncBufReader::new(stdout);
    let mut lines = reader.lines();
    let (tx, rx) = mpsc::channel(32);
    // Spawn a task to read lines and send to channel
    task::spawn(async move {
        while let Ok(Some(line)) = lines.next_line().await {
            let _ = tx.send(line).await;
        }
    });
    Ok(rx)
}
