{"rustc": 1842507548689473721, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 15904350142235955753, "path": 17839063612389316275, "deps": [[3430646239657634944, "build_script_build", false, 12676574980285923667], [7896293946984509699, "bitflags", false, 5042343884753594372], [8253628577145923712, "libc_errno", false, 1532347655318150137], [10281541584571964250, "windows_sys", false, 2415791685121768672]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-0d3883917d208c9d\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}