What is Cygserver?

  Cygserver is a program which is designed to run as a background service.
  It provides Cygwin applications with services which require security
  arbitration or which need to persist while no other cygwin application
  is running.

  The implemented services so far are:

  - Control slave tty/pty handle dispersal from tty owner to other
    processes without compromising the owner processes' security.
  - XSI IPC Message Queues.
  - XSI IPC Semaphores.
  - XSI IPC Shared Memory.
  - Allows non-privileged users to store obfuscated passwords in the
    registry to be used for setuid(2) to create user tokens with network
    credentials.  This service is used by `passwd -R'.  Using the stored
    passwords in setuid(2) does not require running cygserver.  The
    registry storage is the same as Windows uses to store passwords for
    accounts running Windows services.


Cygserver command line options:

  Options to Cygserver take the normal UNIX-style `-X' or `--longoption' form.
  Nearly all options have a counterpart in the configuration file (see below)
  so setting them on the command line isn't really necessary.  Command line
  options override settings from the Cygserver configuration file.

  The one-character options are prepended by a single dash, the long variants
  are prepended with two dashes.  Arguments to options are marked in angle
  brackets below.  These are not part of the actual syntax but are used only to
  denote the arguments.  Note that all arguments are required.  Cygserver
  has no options with optional arguments.
  
  The options recognized are:

  -f, --config-file <file>
  
    Use <file> as configuration file instead of the default configuration
    line.  The default configuration file is /etc/cygserver.conf, typically. 
    The --help and --version options will print the default configuration
    pathname.

    This option has no counterpart in the configuration file, for obvious
   reasons.

  -c, --cleanup-threads <num>  
  
    Number of threads started to perform cleanup tasks.  Default is 2.
    Configuration file option:  kern.srv.cleanup_threads

  -r, --request-threads <num>  
  
    Number of threads started to serve application requests.  Default is 10.
    The -c and -r options can be used to play with Cygserver's performance
    under heavy load conditions or on slow machines.
    Configuration file option:  kern.srv.request_threads

  -p, --process-cache <num>

    Number of processes which can connect concurrently to cygserver.
    Default is 62.  Each process connected to cygserver is a synchronization
    object which has to be maintained.  The data structure to maintain these
    processes is the so-called "process cache".  In theory, an arbitrary
    number of processes could connect to cygserver, but due to the need to
    synchronize, the higher the number of connected processes, the more
    synchronization overhead exists.  By using this option, you can set an
    upper limit to the synchronization effort.  If more than 62 processes
    try to connect to cygserver concurrently, two additional synchronization
    threads are necessary, and one for each further 62 concurrent
    processes.  So, useful values for the --process-cache option are 62, 124,
    186, 248, 310.  310 is the maximum value.
    Configuration file option:  kern.srv.process_cache_size

    NOTE:  The number of child processes of a single parent process is limited
    to 256.  So in case of taking advantage of a process cache size beyond 256,
    keep in mind that not all of these processes can be child processes of one
    single parent process.

  -d, --debug
  
    Log debug messages to stderr.  These will clutter your stderr output with
    a lot of information, typically only useful to developers.

  -e, --stderr
  
    Force logging to stderr.  This is the default if stderr is connected to
    a tty.  Otherwise, the default is logging to the system log.  By using
    the -e, -E, -y, -Y options (or the appropriate settings in the
    configuration file), you can explicitely set the logging output as you
    like, even to both, stderr and syslog.
    Configuration file option:  kern.log.stderr

  -E, --no-stderr
  
    Don't log to stderr.  Configuration file option:  kern.log.stderr

  -y, --syslog
  
    Force logging to the system log.  This is the default, if stderr is not
    connected to a tty, e. g. redirected to a file.

  -Y, --no-syslog
  
    Don't log to syslog.  Configuration file option:  kern.log.syslog

  -l, --log-level <level>
  
    Set the verbosity level of the logging output.  Valid values are between
    1 and 7.  The default level is 6, which is relatively chatty.  If you set
    it to 1, you will get only messages which are printed under severe conditions,
    which will result in stopping Cygserver itself.
    Configuration file option:  kern.log.level

  -m, --no-sharedmem
  
    Don't start XSI IPC Shared Memory support.  If you don't need XSI IPC
    Shared Memory support, you can switch it off here.
    Configuration file option:  kern.srv.sharedmem

  -q, --no-msgqueues
  
    Don't start XSI IPC Message Queues. 
    Configuration file option:  kern.srv.msgqueues

  -s, --no-semaphores
  
    Don't start XSI IPC Semaphores.
    Configuration file option:  kern.srv.semaphores

  -S, --shutdown
  
    Shutdown a running daemon and exit.  Other methods are sending a SIGHUP
    to the Cygserver PID or, if running as service under NT, calling
    `net stop cygserver' or `cygrunsrv -E cygserver'.

  -h, --help
  
    Output usage information and exit.

  -v, --version
  
    Output version information and exit.


How to start Cygserver:

  Before you run Cygserver for the first time, you should run the
  /usr/bin/cygserver-config script once.  It creates the default
  configuration file and, upon request, installs Cygserver as service
  when running under NT.  The script only performs a default install,
  with no further options given to Cygserver when running as service.
  Due to the wide configurability by changing the configuration file,
  that's typically not necessary.

  It's best practice to run Cygserver as a service under LocalSystem
  account.  This is the way it is installed for you by the
  /usr/bin/cygserver-config script.


The Cygserver configuration file:

  Cygserver has many options, which allow to customize the server
  to your needs.  Customization is accomplished by editing the configuration
  file, which is by default /etc/cygserver.conf.  This file is read only
  once on startup of Cygserver.  There's no option to re-read the file on
  runtime by, say, sending a signal to Cygserver.

  The configuration file determines how Cygserver operates.  There are
  options which set the number of threads running in parallel, options
  for setting how and what to log and options to set various maximum
  values for the IPC services.

  The default configuration file delivered with Cygserver is installed
  to /etc/defaults/etc.  The /usr/bin/cygserver-config script copies it to
  /etc, giving you the option to overwrite an already existing file or to
  leave it alone.  Therefore, the /etc file is safe to be changed by you,
  since it will not be overwritten by a later update installation.

  The default configuration file contains many comments which describe
  everything needed to understand the settings.  A comment at the start of the
  file describes the syntax rules for the file.  The default options are shown
  in the file but are commented out.

  It is generally a good idea to uncomment only options which you intend to
  change from the default values.  Since reading the options file on Cygserver
  startup doesn't take much time, it's also considered good practice to keep
  all other comments in the file.  This keeps you from searching for clues
  in other sources.


If you have problems with Cygserver, or you have found a bug, or you
think you have found a bug, or you don't understand configuration file
options, the mailing list <<EMAIL>> is the right place to ask
questions.

Have fun!
