Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: 9f68c864de34486d19ba217f7f708d26c2a91caf4c1b5bbe4b86c788d0839ae1
Status: install ok installed

Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: 331025c6cff9f70d0010021a22bfe6e061ba7b2b425ee823f220d82dfd4413dc
Status: install ok installed

Package: sdl2
Version: 2.32.8
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 9196700a3fb4aec74297378aa63562c0717e1b862673a3d6201a019726f5f146
Description: Simple DirectMedia Layer is a cross-platform development library designed to provide low level access to audio, keyboard, mouse, joystick, and graphics hardware via OpenGL and Direct3D.
Status: install ok installed

