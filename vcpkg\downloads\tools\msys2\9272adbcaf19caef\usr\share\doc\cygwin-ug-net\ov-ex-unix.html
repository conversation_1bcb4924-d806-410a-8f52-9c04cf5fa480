<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Quick Start Guide for those more experienced with UNIX</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"><link rel="prev" href="ov-ex-win.html" title="Quick Start Guide for those more experienced with Windows"><link rel="next" href="are-free.html" title="Are the Cygwin tools free software?"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Quick Start Guide for those more experienced with UNIX</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="ov-ex-win.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Cygwin Overview</th><td width="20%" align="right">&#160;<a accesskey="n" href="are-free.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ov-ex-unix"></a>Quick Start Guide for those more experienced with UNIX</h2></div></div></div><p>
If you are an experienced UNIX user who misses a powerful command-line
environment, you will enjoy Cygwin.
Developers coming from a UNIX background will find a set of utilities
they are already comfortable using, including a working UNIX shell.  The
compiler tools are the standard GNU compilers most people will have previously
used under UNIX, only ported to the Windows host.  Programmers wishing to port
UNIX software to Windows NT will find that the Cygwin library provides
an easy way to port many UNIX packages, with only minimal source code
changes.
</p><p>
Note that there are some workarounds that cause Cygwin to behave differently
than most UNIX-like operating systems; these are described in more detail in 
<a class="xref" href="using-effectively.html" title="Using Cygwin effectively with Windows">the section called &#8220;Using Cygwin effectively with Windows&#8221;</a>.
</p><p>
Use the graphical Cygwin Setup program (available from the
<a class="ulink" href="https://cygwin.com/" target="_top">Cygwin homepage</a>)
any time you want to update or install a Cygwin package.  This program must
be run manually every time you want to check for updated packages since Cygwin
does not currently include a mechanism for automatically detecting
package updates.
</p><p>
By default, the Cygwin Setup program only installs a minimal subset of
packages.  Add any other packages by clicking on the <code class="literal">+</code>
next to the Category name and selecting the package from the displayed
list.  You may search for specific tools by using the
<a class="ulink" href="https://cygwin.com/packages/" target="_top">Setup Package Search</a>
at the Cygwin web site.
</p><p>
After installation, you can find Cygwin-specific documentation in
the <code class="literal">/usr/share/doc/Cygwin/</code> directory.
</p><p>
For more information about what each option in Cygwin Setup means,
see <a class="xref" href="setup-net.html#internet-setup" title="Internet Setup">the section called &#8220;Internet Setup&#8221;</a>.
</p></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="ov-ex-win.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="overview.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="are-free.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Quick Start Guide for those more experienced with Windows&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Are the Cygwin tools free software?</td></tr></table></div></body></html>
