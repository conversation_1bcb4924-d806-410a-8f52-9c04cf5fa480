{"rustc": 1842507548689473721, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 15313785834954617719, "deps": [[40386456601120721, "percent_encoding", false, 9982838754098454369], [784494742817713399, "tower_service", false, 12656429896994559370], [1811549171721445101, "futures_channel", false, 14243427294437473204], [1906322745568073236, "pin_project_lite", false, 7915125176091775041], [2054153378684941554, "tower_http", false, 5814867117496069201], [2517136641825875337, "sync_wrapper", false, 14410449616987650332], [2883436298747778685, "rustls_pki_types", false, 12093203290534274294], [3150220818285335163, "url", false, 11430924996921804735], [5695049318159433696, "tower", false, 5243326144983368698], [5986029879202738730, "log", false, 18342494332034957139], [7620660491849607393, "futures_core", false, 13296020411631890703], [9010263965687315507, "http", false, 12375921189999122163], [9689903380558560274, "serde", false, 3932883301695986267], [10229185211513642314, "mime", false, 825014560901657502], [10629569228670356391, "futures_util", false, 14657915111446951667], [11957360342995674422, "hyper", false, 4196829220126493810], [12186126227181294540, "tokio_native_tls", false, 5947392054834942335], [12393800526703971956, "tokio", false, 11632526497277506854], [13077212702700853852, "base64", false, 12309863791170057922], [14084095096285906100, "http_body", false, 16101576858933787663], [14359893265615549706, "h2", false, 2821857095453170539], [14564311161534545801, "encoding_rs", false, 8180493177510039276], [15367738274754116744, "serde_json", false, 2136575339520338002], [16066129441945555748, "bytes", false, 15933313523296886668], [16542808166767769916, "serde_urlencoded", false, 1111178962613177724], [16680807377217054954, "hyper_util", false, 14543189545182698206], [16785601910559813697, "native_tls_crate", false, 2876266168373421446], [16900715236047033623, "http_body_util", false, 4714452990932040847], [18273243456331255970, "hyper_tls", false, 18153882831820963308]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-097b8f40b7dc0dec\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}