{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 16971202462324950932, "deps": [[5103565458935487, "futures_io", false, 17343819074470059527], [1811549171721445101, "futures_channel", false, 14243427294437473204], [7013762810557009322, "futures_sink", false, 8335408254793702234], [7620660491849607393, "futures_core", false, 13296020411631890703], [10629569228670356391, "futures_util", false, 14657915111446951667], [12779779637805422465, "futures_executor", false, 16558172661141824588], [16240732885093539806, "futures_task", false, 14683701586217419152]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-e29feb40c736d30f\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}