C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/Experimental.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/Nightly.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/Continuous.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/NightlyMemoryCheck.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/ALL_BUILD.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/ZERO_CHECK.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/CMakeFiles/ALL_BUILD.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/ggml-base.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/ggml.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/ggml-cpu.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/ggml-cpu/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/ggml-cpu/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/src/CMakeFiles/whisper.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/src/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/src/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/CMakeFiles/test-vad.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/CMakeFiles/test-vad-full.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/common.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/common-sdl.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cli/CMakeFiles/whisper-cli.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cli/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cli/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/bench/CMakeFiles/whisper-bench.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/bench/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/bench/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/server/CMakeFiles/whisper-server.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/server/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/server/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/quantize/CMakeFiles/quantize.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/quantize/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/quantize/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/vad-speech-segments/CMakeFiles/vad-speech-segments.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/vad-speech-segments/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/vad-speech-segments/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/stream/CMakeFiles/whisper-stream.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/stream/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/stream/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/command/CMakeFiles/whisper-command.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/command/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/command/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/talk-llama/CMakeFiles/whisper-talk-llama.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/talk-llama/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/talk-llama/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/lsp/CMakeFiles/lsp.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/lsp/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/lsp/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/main.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/bench.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/stream.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/command.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/libwchess/CMakeFiles/wchess-core.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/libwchess/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/libwchess/CMakeFiles/INSTALL.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/wchess.cmd/CMakeFiles/wchess.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/wchess.cmd/CMakeFiles/RUN_TESTS.dir
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/wchess.cmd/CMakeFiles/INSTALL.dir
