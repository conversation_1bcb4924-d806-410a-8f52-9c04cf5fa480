'\" t
.\"     Title: umount
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "UMOUNT" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
umount \- Unmount filesystems
.SH "SYNOPSIS"
.HP \w'\fBumount\fR\ 'u
\fBumount\fR \fIPOSIXPATH\fR
.HP \w'\fBumount\fR\ 'u
\fBumount\fR \-U
.HP \w'\fBumount\fR\ 'u
\fBumount\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
  \-h, \-\-help                    output usage information and exit
  \-U, \-\-remove\-user\-mounts      remove all user mounts
  \-V, \-\-version                 output version information and exit
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The
\fBumount\fR
program removes mounts from the mount table in the current session\&. If you specify a POSIX path that corresponds to a current mount point,
\fBumount\fR
will remove it from the current mount table\&. Note that you can only remove user mount points\&. The
\-U
flag may be used to specify removing all user mount points from the current user session\&.
.PP
See
the section called \(lqThe Cygwin Mount Table\(rq
for more information on the mount table\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
