{"rustc": 1842507548689473721, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 15313785834954617719, "deps": [[40386456601120721, "percent_encoding", false, 5463949068515033110], [784494742817713399, "tower_service", false, 6656028162146656903], [1811549171721445101, "futures_channel", false, 11034357251717201261], [1906322745568073236, "pin_project_lite", false, 15321155184145598939], [2054153378684941554, "tower_http", false, 4770553382319950657], [2517136641825875337, "sync_wrapper", false, 6407803636242632466], [2883436298747778685, "rustls_pki_types", false, 2385293034048276101], [3150220818285335163, "url", false, 6598206335186101349], [5695049318159433696, "tower", false, 9156706038949686014], [5986029879202738730, "log", false, 13745748516531157483], [7620660491849607393, "futures_core", false, 10672795016643040551], [9010263965687315507, "http", false, 17721166657450571867], [9689903380558560274, "serde", false, 229646397861904564], [10229185211513642314, "mime", false, 1123077221550873118], [10629569228670356391, "futures_util", false, 10146807928191515444], [11957360342995674422, "hyper", false, 253499875597335222], [12186126227181294540, "tokio_native_tls", false, 4245485387820166995], [12393800526703971956, "tokio", false, 10438108520933726755], [13077212702700853852, "base64", false, 10007374116913001356], [14084095096285906100, "http_body", false, 11490386077403768983], [14359893265615549706, "h2", false, 3342418490026650224], [14564311161534545801, "encoding_rs", false, 15283220100992779186], [15367738274754116744, "serde_json", false, 16543861303144050872], [16066129441945555748, "bytes", false, 1351185118277919725], [16542808166767769916, "serde_urlencoded", false, 17150122474456913743], [16680807377217054954, "hyper_util", false, 6389976216642182574], [16785601910559813697, "native_tls_crate", false, 13471850481852567877], [16900715236047033623, "http_body_util", false, 14322989347754011600], [18273243456331255970, "hyper_tls", false, 9163296497622582585]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-f5c06f9aeb2bda81\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}