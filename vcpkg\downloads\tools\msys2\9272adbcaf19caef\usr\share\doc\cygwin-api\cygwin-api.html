<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Cygwin API Reference</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><meta name="description" content="Cygwin API Reference"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="next" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Cygwin API Reference</th></tr><tr><td width="20%" align="left">&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="compatibility.html">Next</a></td></tr></table><hr></div><div class="book"><div class="titlepage"><div><div><h1 class="title"><a name="cygwin-api"></a>Cygwin API Reference</h1></div><div><div class="legalnotice"><a name="legal"></a><p>Copyright &#169; Cygwin authors</p><p>Permission is granted to make and distribute verbatim copies of
this documentation provided the copyright notice and this permission
notice are preserved on all copies.</p><p>Permission is granted to copy and distribute modified versions
of this documentation under the conditions for verbatim copying,
provided that the entire resulting derived work is distributed under
the terms of a permission notice identical to this one.</p><p>Permission is granted to copy and distribute translations of
this documentation into another language, under the above conditions
for modified versions, except that this permission notice may be
stated in a translation approved by the Free Software
Foundation.</p></div></div><div><div class="abstract"><p class="title"><b>Abstract</b></p><p>Cygwin API Reference</p></div></div></div><hr></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="chapter"><a href="compatibility.html">1. Compatibility</a></span></dt><dd><dl><dt><span class="sect1"><a href="compatibility.html#std-susv5">System interfaces compatible with the Single UNIX&#174; Specification Version 5:</a></span></dt><dt><span class="sect1"><a href="std-bsd.html">System interfaces compatible with BSD functions:</a></span></dt><dt><span class="sect1"><a href="std-gnu.html">System interfaces compatible with GNU or Linux extensions:</a></span></dt><dt><span class="sect1"><a href="std-solaris.html">System interfaces compatible with Solaris or SunOS functions:</a></span></dt><dt><span class="sect1"><a href="std-iso.html">System interfaces not in POSIX but compatible with ISO C requirements:</a></span></dt><dt><span class="sect1"><a href="std-deprec.html">Other UNIX&#174; system interfaces, not in POSIX.1-2024, or deprecated:</a></span></dt><dt><span class="sect1"><a href="std-notimpl.html">NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:</a></span></dt><dt><span class="sect1"><a href="std-other.html">Other system interfaces, some from Windows:</a></span></dt><dt><span class="sect1"><a href="std-notes.html">Implementation Notes</a></span></dt></dl></dd><dt><span class="chapter"><a href="cygwin-functions.html">2. Cygwin Functions</a></span></dt><dd><dl><dt><span class="sect1"><a href="cygwin-functions.html#func-cygwin-path">Path conversion functions</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-conv-path.html">cygwin_conv_path</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-conv-path-list.html">cygwin_conv_path_list</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-create-path.html">cygwin_create_path</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-posix-path-list-p.html">cygwin_posix_path_list_p</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-split-path.html">cygwin_split_path</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="func-cygwin-login.html">Helper functions to change user context</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-logon_user.html">cygwin_logon_user</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-set-impersonation-token.html">cygwin_set_impersonation_token</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="func-cygwin-misc.html">Miscellaneous functions</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-attach-handle-to-fd.html">cygwin_attach_handle_to_fd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-internal.html">cygwin_internal</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-stackdump.html">cygwin_stackdump</a></span><span class="refpurpose"></span></dt></dl></dd></dl></dd></dl></div><div class="list-of-examples"><p><b>List of Examples</b></p><dl><dt>2.1. <a href="func-cygwin-conv-path.html#func-cygwin-conv-path-example-example">Example use of cygwin_conv_path</a></dt><dt>2.2. <a href="func-cygwin-split-path.html#func-cygwin-split-path-example-example">Example use of cygwin_split_path</a></dt></dl></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left">&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="compatibility.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right" valign="top">&#160;Chapter&#160;1.&#160;Compatibility</td></tr></table></div></body></html>
