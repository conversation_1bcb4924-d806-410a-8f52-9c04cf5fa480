{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2241668132362809309, "path": 2426152907218305016, "deps": [[2828590642173593838, "cfg_if", false, 635313102029775498], [5491919304041016563, "build_script_build", false, 16569066244982076573], [8995469080876806959, "untrusted", false, 4537688563754723624], [9920160576179037441, "getrandom", false, 15596862095367603574]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-3b77a0adbd54249c\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}