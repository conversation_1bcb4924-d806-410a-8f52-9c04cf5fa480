'\" t
.\"     Title: cygwin_conv_path_list
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin API Reference
.\"    Source: Cygwin API Reference
.\"  Language: English
.\"
.TH "CYGWIN_CONV_PATH_LIST" "3" "06/03/2025" "Cygwin API Reference" "Cygwin API Reference"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
cygwin_conv_path_list
.SH "SYNOPSIS"
.sp
.ft B
.nf
#include <sys/cygwin\&.h>
.fi
.ft
.HP \w'ssize_t\ cygwin_conv_path_list('u
.BI "ssize_t cygwin_conv_path_list(cygwin_conv_path_t\ " "what" ", const\ void\ *\ " "from" ", void\ *\ " "to" ", size_t\ " "size" ");"
.SH "DESCRIPTION"
.PP
This is the same as
\fBcygwin_conv_path\fR, but the input is treated as a path list in $PATH or %PATH% notation\&.
.PP
If
\fIwhat\fR
is CCP_POSIX_TO_WIN_A or CCP_POSIX_TO_WIN_W, given a POSIX $PATH\-style string (i\&.e\&. /foo:/bar) convert it to the equivalent Win32 %PATH%\-style string (i\&.e\&. d:\e;e:\ebar)\&.
.PP
If
\fIwhat\fR
is CCP_WIN_A_TO_POSIX or CCP_WIN_W_TO_POSIX, given a Win32 %PATH%\-style string (i\&.e\&. d:\e;e:\ebar) convert it to the equivalent POSIX $PATH\-style string (i\&.e\&. /foo:/bar)\&.
.PP
\fIsize\fR
is the size of the buffer pointed to by
\fIto\fR
in bytes\&.
.SH "SEE ALSO"
.PP
See also
cygwin_conv_path
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
