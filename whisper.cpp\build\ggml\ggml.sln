﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{3B3DE00C-3AA3-3496-A11E-2943056593A5}"
	ProjectSection(ProjectDependencies) = postProject
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
		{5D87EF70-0724-323B-B1C8-DCA86C63702E} = {5D87EF70-0724-323B-B1C8-DCA86C63702E}
		{6466B51B-1D59-359A-9311-E09F9F049945} = {6466B51B-1D59-359A-9311-E09F9F049945}
		{2B34292D-44F8-373E-8DDF-486F9C9525C1} = {2B34292D-44F8-373E-8DDF-486F9C9525C1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{6ABA59C0-4E80-3B69-AFAA-42EF48FBDD28}"
	ProjectSection(ProjectDependencies) = postProject
		{3B3DE00C-3AA3-3496-A11E-2943056593A5} = {3B3DE00C-3AA3-3496-A11E-2943056593A5}
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{5191E162-B61D-3990-B3B9-1B57F568CB06}"
	ProjectSection(ProjectDependencies) = postProject
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{4C435E7B-E811-3F09-974A-C0840C54E5D9}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml", "src\ggml.vcxproj", "{5D87EF70-0724-323B-B1C8-DCA86C63702E}"
	ProjectSection(ProjectDependencies) = postProject
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
		{6466B51B-1D59-359A-9311-E09F9F049945} = {6466B51B-1D59-359A-9311-E09F9F049945}
		{2B34292D-44F8-373E-8DDF-486F9C9525C1} = {2B34292D-44F8-373E-8DDF-486F9C9525C1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-base", "src\ggml-base.vcxproj", "{6466B51B-1D59-359A-9311-E09F9F049945}"
	ProjectSection(ProjectDependencies) = postProject
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ggml-cpu", "src\ggml-cpu.vcxproj", "{2B34292D-44F8-373E-8DDF-486F9C9525C1}"
	ProjectSection(ProjectDependencies) = postProject
		{4C435E7B-E811-3F09-974A-C0840C54E5D9} = {4C435E7B-E811-3F09-974A-C0840C54E5D9}
		{6466B51B-1D59-359A-9311-E09F9F049945} = {6466B51B-1D59-359A-9311-E09F9F049945}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.Debug|x64.ActiveCfg = Debug|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.Debug|x64.Build.0 = Debug|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.Release|x64.ActiveCfg = Release|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.Release|x64.Build.0 = Release|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3B3DE00C-3AA3-3496-A11E-2943056593A5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6ABA59C0-4E80-3B69-AFAA-42EF48FBDD28}.Debug|x64.ActiveCfg = Debug|x64
		{6ABA59C0-4E80-3B69-AFAA-42EF48FBDD28}.Release|x64.ActiveCfg = Release|x64
		{6ABA59C0-4E80-3B69-AFAA-42EF48FBDD28}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6ABA59C0-4E80-3B69-AFAA-42EF48FBDD28}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5191E162-B61D-3990-B3B9-1B57F568CB06}.Debug|x64.ActiveCfg = Debug|x64
		{5191E162-B61D-3990-B3B9-1B57F568CB06}.Release|x64.ActiveCfg = Release|x64
		{5191E162-B61D-3990-B3B9-1B57F568CB06}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5191E162-B61D-3990-B3B9-1B57F568CB06}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.Debug|x64.ActiveCfg = Debug|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.Debug|x64.Build.0 = Debug|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.Release|x64.ActiveCfg = Release|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.Release|x64.Build.0 = Release|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4C435E7B-E811-3F09-974A-C0840C54E5D9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.Debug|x64.ActiveCfg = Debug|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.Debug|x64.Build.0 = Debug|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.Release|x64.ActiveCfg = Release|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.Release|x64.Build.0 = Release|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5D87EF70-0724-323B-B1C8-DCA86C63702E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.Debug|x64.ActiveCfg = Debug|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.Debug|x64.Build.0 = Debug|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.Release|x64.ActiveCfg = Release|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.Release|x64.Build.0 = Release|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6466B51B-1D59-359A-9311-E09F9F049945}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.Debug|x64.ActiveCfg = Debug|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.Debug|x64.Build.0 = Debug|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.Release|x64.ActiveCfg = Release|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.Release|x64.Build.0 = Release|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2B34292D-44F8-373E-8DDF-486F9C9525C1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4175ADD7-59AA-349E-B876-DA06420A3F90}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
