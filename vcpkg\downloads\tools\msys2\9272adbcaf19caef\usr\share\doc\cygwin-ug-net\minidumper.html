<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>minidumper</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="lsattr.html" title="lsattr"><link rel="next" href="mkgroup.html" title="mkgroup"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">minidumper</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="lsattr.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="mkgroup.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="minidumper"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>minidumper &#8212; Write minidump from WIN32PID to FILENAME.dmp</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">minidumper</code>  [-d] [-n] [-q] [-t <em class="replaceable"><code>TYPE</code></em>]  <em class="replaceable"><code>FILENAME</code></em>   <em class="replaceable"><code>WIN32PID</code></em> </p></div><div class="cmdsynopsis"><p><code class="command">minidumper</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="minidumper-options"></a><h2>Options</h2><pre class="screen">
-t, --type     minidump type flags
-n, --nokill   don't terminate the dumped process
-d, --verbose  be verbose while dumping
-h, --help     output help information and exit
-q, --quiet    be quiet while dumping (default)
-V, --version  output version information and exit
  </pre></div><div class="refsect1"><a name="minidumper-desc"></a><h2>Description</h2><p>
    The <span class="command"><strong>minidumper</strong></span> utility can be used to create a
    minidump of a running Windows process.  This minidump can be later
    analysed using breakpad or Windows debugging tools.
  </p><p>
    <span class="command"><strong>minidumper</strong></span> can be used with cygwin's Just-In-Time
    debugging facility by adding <code class="code">error_start=minidumper</code> to the
    <code class="literal">CYGWIN</code> environment variable. If <code class="literal">CYGWIN</code>
    is set this way, then <span class="command"><strong>minidumper</strong></span> will be started whenever
    a program encounters a fatal exception.
  </p><p>
    <span class="command"><strong>minidumper</strong></span> can also be started from the command line to
    create a minidump of any running process.  For compatibility with
    <span class="command"><strong>dumper</strong></span> the target process is terminated after dumping
    unless the <code class="literal">-n</code> option is given.
  </p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="lsattr.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="mkgroup.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">lsattr&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;mkgroup</td></tr></table></div></body></html>
