'\" t
.\"     Title: tzset
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "TZSET" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
tzset \- Print POSIX\-compatible timezone ID from current Windows timezone setting
.SH "SYNOPSIS"
.HP \w'\fBtzset\fR\ 'u
\fBtzset\fR [\-h | \-V]
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
Options:
  \-h, \-\-help               output usage information and exit\&.
  \-V, \-\-version            output version information and exit\&.
      
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
Use tzset to set your TZ variable\&. In POSIX\-compatible shells like bash, dash, mksh, or zsh:
.sp
.if n \{\
.RS 4
.\}
.nf
export TZ=$(tzset)
      
.fi
.if n \{\
.RE
.\}
.sp
In csh\-compatible shells like tcsh:
.sp
.if n \{\
.RS 4
.\}
.nf
setenv TZ `tzset`
      
.fi
.if n \{\
.RE
.\}
.PP
The
\fBtzset\fR
tool reads the current timezone from Windows and generates a POSIX\-compatible timezone information for the TZ environment variable from that information\&. That\*(Aqs all there is to it\&. For the way how to use it, see the above usage information\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
