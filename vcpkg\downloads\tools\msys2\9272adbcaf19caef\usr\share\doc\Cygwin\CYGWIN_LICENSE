THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESSED OR
IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.

Unless stated otherwise, the sources under the cygwin subdirectory,
as well as the sources under the cygserver subdirectory linked into
the Cygwin DLL, are licensed under the Lesser Gnu Public License,
version 3 or (at your option) any later version (LGPLv3+).  See the
COPYING.LIB file for the exact wording of that license.

Unless stated otherwise, the sources under the cygserver subdir not
linked into the Cygwin DLL, as well as the sources under the lsaauth
and the utils subdirectories are licensed under the Gnu Public License,
version 3 or (at your option) any later version (GPLv3+).  See the
COPYING file for the exact wording of that license. 

Parts of the sources in any subdirectory are licensed using a BSD-like
license.  The affected source files contain explicit copyright notices
to that effect.

Linking Exception:

  As a special exception, the copyright holders of the Cygwin library
  grant you additional permission to link libcygwin.a, crt0.o, and
  gcrt0.o with independent modules to produce an executable, and to
  convey the resulting executable under terms of your choice, without
  any need to comply with the conditions of LGPLv3 section 4. An
  independent module is a module which is not itself based on the
  Cygwin library.

