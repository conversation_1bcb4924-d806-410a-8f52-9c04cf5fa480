{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 8277825411580366689, "profile": 8731458305071235362, "path": 17266458284092215070, "deps": [[1216658119993006735, "piper_module", false, 814648621570352266], [1760623714118191065, "dotenv", false, 18279243410964301757], [1994437411054725675, "gemini_module", false, 14240783656397082139], [2941951222343019209, "notify", false, 8443126333207291698], [5265161007890236711, "fastrtc_bridge", false, 15572146595815922706], [9614479274285663593, "serde_yaml", false, 16356777146844313507], [9689903380558560274, "serde", false, 229646397861904564], [12393800526703971956, "tokio", false, 10438108520933726755], [13625485746686963219, "anyhow", false, 12059747807702860232], [14791524771484756873, "whisper_module", false, 15279927209357533764], [15367738274754116744, "serde_json", false, 16543861303144050872], [17907286668138153651, "agent_tools", false, 17888188392849818571]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\voice_agent-15d322918637cbcc\\dep-bin-voice_agent", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}