'\" t
.\"     Title: newgrp
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "NEWGRP" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
newgrp \- change primary group for a command
.SH "SYNOPSIS"
.HP \w'\fBnewgrp\fR\ 'u
\fBnewgrp\fR [\-] [\fIgroup\fR] [\fIcommand\fR\ [\fIargs\fR...]]
.SH "DESCRIPTION"
.PP
\fBnewgrp\fR
changes the primary group for a command\&.
.PP
If the
\fB\-\fR
flag is given as first argument, the user\*(Aqs environment will be reinitialized as though the user had logged in, otherwise the current environment, including current working directory, remains unchanged\&.
.PP
\fBnewgrp\fR
changes the current primary group to the named group, or to the default group listed in /etc/passwd if no group name is given\&.
.PP
By default, the user\*(Aqs standard shell is started, called as login shell if the
\fB\-\fR
flag has been specified\&. If a group has been given as argument, a command and its arguments can be specified on the command line\&.
.PP
The new primary group must be either the old primary group, or it must be part of the supplementary group list\&. Setting the primary group to an arbitrary group is not allowed in Windows\&.
.SH "SEE ALSO"
.PP
\fBid\fR(1),
\fBlogin\fR(1)\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
