<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_stackdump</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="func-cygwin-misc.html" title="Miscellaneous functions"><link rel="prev" href="func-cygwin-internal.html" title="cygwin_internal"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_stackdump</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-internal.html">Prev</a>&#160;</td><th width="60%" align="center">Miscellaneous functions</th><td width="20%" align="right">&#160;</td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-stackdump"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_stackdump</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">void
<b class="fsfunc">cygwin_stackdump</b>(</code><code>void)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-stackdump-desc"></a><h2>Description</h2><p> Outputs a stackdump to stderr from the called location.
</p><p> Note: This function only has an effect the first time it is called by a process.
</p><p> Note: This function is deprecated.
</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-internal.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="func-cygwin-misc.html">Up</a></td><td width="40%" align="right">&#160;</td></tr><tr><td width="40%" align="left" valign="top">cygwin_internal&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;</td></tr></table></div></body></html>
