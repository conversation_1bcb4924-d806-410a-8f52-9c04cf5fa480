'\" t
.\"     Title: locale
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "LOCALE" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
locale \- Get locale\-specific information
.SH "SYNOPSIS"
.HP \w'\fBlocale\fR\ 'u
\fBlocale\fR [\-amvhV]
.HP \w'\fBlocale\fR\ 'u
\fBlocale\fR [\-ck] \fINAME\fR
.HP \w'\fBlocale\fR\ 'u
\fBlocale\fR [\-iusfnU]
.HP \w'\fBlocale\fR\ 'u
\fBlocale\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
System information:

  \-a, \-\-all\-locales    List all available supported locales
  \-m, \-\-charmaps       List all available character maps
  \-v, \-\-verbose        More verbose output

Modify output format:

  \-c, \-\-category\-name  List information about given category NAME
  \-k, \-\-keyword\-name   Print information about given keyword NAME

Default locale information:

  \-i, \-\-input          Print current input locale
  \-u, \-\-user           Print locale of user\*(Aqs default UI language
  \-s, \-\-system         Print locale of system default UI language
  \-f, \-\-format         Print locale of user\*(Aqs regional format settings
                       (time, numeric & monetary)
  \-n, \-\-no\-unicode     Print system default locale for non\-Unicode programs
  \-U, \-\-utf            Attach \e"\&.UTF\-8\e" to the result

Other options:

  \-h, \-\-help           This text
  \-V, \-\-version        Print program version and exit
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
\fBlocale\fR
without parameters prints information about the current locale environment settings\&.
.PP
The
\-i,
\-u,
\-s,
\-f, and
\-n
options can be used to request the various Windows locale settings\&. The purpose is to use this command in scripts to set the POSIX locale variables\&.
.PP
The
\-i
option prints the current input language\&. This is called the "Input language" and basically equivalent to the current keyboard layout setting\&.
.PP
The
\-u
option prints the current user\*(Aqs Windows UI locale to stdout\&. In Windows this setting is called the "Display Language"\&.
.PP
The
\-s
option prints the systems default instead\&.
.PP
The
\-f
option prints the user\*(Aqs setting for time, date, number and currency\&. That\*(Aqs equivalent to the setting in the "Formats" or "Regional Options" tab in the "Region and Language" or "Regional and Language Options" dialog\&.
.PP
The
\-n
option prints the system\*(Aqs default language used for applications which don\*(Aqt support Unicode\&.
.PP
With the
\-U
option
\fBlocale\fR
appends the string "\&.UTF\-8" to enforce using UTF\-8\&. Using UTF\-8 as codeset is recommended\&.
.PP
Usage example:
.sp
.if n \{\
.RS 4
.\}
.nf
bash$ export LANG=$(locale \-uU)
bash$ echo $LANG
en_US\&.UTF\-8
bash$ export LC_TIME=$(locale \-fU)
bash$ echo $LC_TIME
de_DE\&.UTF\-8
.fi
.if n \{\
.RE
.\}
.PP
The
\-a
option is helpful to learn which locales are supported by your Windows machine\&. It prints all available locales and the allowed modifiers\&. Example:
.sp
.if n \{\
.RS 4
.\}
.nf
bash$ locale \-a
C
C\&.utf8
POSIX
af_ZA
af_ZA\&.utf8
am_ET
am_ET\&.utf8
\&.\&.\&.
be_BY
be_BY\&.utf8
be_BY@latin
\&.\&.\&.
ca_ES
ca_ES\&.utf8
ca_ES@euro
catalan
\&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
The
\-v
option prints more detailed information about each available locale\&. Example:
.sp
.if n \{\
.RS 4
.\}
.nf
bash$ locale \-av
locale: af_ZA           archive: /cygdrive/c/Windows/system32/kernel32\&.dll
\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-
 language | Afrikaans
territory | South Africa
  codeset | ISO\-8859\-1

locale: af_ZA\&.utf8      archive: /cygdrive/c/Windows/system32/kernel32\&.dll
\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-
 language | Afrikaans
territory | South Africa
  codeset | UTF\-8

\&.\&.\&.

locale: ca_ES@euro      archive: /cygdrive/c/Windows/system32/kernel32\&.dll
\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-
 language | Catalan
territory | Spain
  codeset | ISO\-8859\-15

locale: catalan         archive: /usr/share/locale/locale\&.alias
\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-
 language | Catalan
territory | Spain
  codeset | ISO\-8859\-1

\&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
The
\-m
option prints the names of the available charmaps supported by Cygwin to stdout\&.
.PP
Otherwise, if arguments are given,
\fBlocale\fR
prints the values assigned to these arguments\&. Arguments can be names of locale categories (for instance: LC_CTYPE, LC_MONETARY), or names of keywords supported in the locale categories (for instance: thousands_sep, charmap)\&. The
\-c
option prints additionally the name of the category\&. The
\-k
option prints additionally the name of the keyword\&. Example:
.sp
.if n \{\
.RS 4
.\}
.nf
bash$ locale \-ck LC_MESSAGES
LC_MESSAGES
yesexpr="^[yY]"
noexpr="^[nN]"
yesstr="yes"
nostr="no"
messages\-codeset="UTF\-8"
bash$ locale noexpr
^[nN]
    
.fi
.if n \{\
.RE
.\}
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
