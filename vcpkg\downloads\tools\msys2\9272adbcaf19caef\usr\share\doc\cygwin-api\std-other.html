<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Other system interfaces, some from Windows:</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="prev" href="std-notimpl.html" title="NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:"><link rel="next" href="std-notes.html" title="Implementation Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Other system interfaces, some from Windows:</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="std-notimpl.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Compatibility</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-notes.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-other"></a>Other system interfaces, some from Windows:</h2></div></div></div><pre class="screen">
    _get_osfhandle             (Windows)
    _setmode                   (Windows)
    cwait                      (Windows)
    cygwin_attach_handle_to_fd
    cygwin_conv_path
    cygwin_conv_path_list
    cygwin_create_path
    cygwin_detach_dll
    cygwin_dll_init
    cygwin_internal
    cygwin_logon_user
    cygwin_posix_path_list_p
    cygwin_set_impersonation_token
    cygwin_split_path
    cygwin_stackdump
    cygwin_umount
    cygwin_winpid_to_pid
    spawnl                     (Windows)
    spawnle                    (Windows)
    spawnlp                    (Windows)
    spawnlpe                   (Windows)
    spawnv                     (Windows)
    spawnve                    (Windows)
    spawnvp                    (Windows)
    spawnvpe                   (Windows)
</pre></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="std-notimpl.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="compatibility.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="std-notes.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Implementation Notes</td></tr></table></div></body></html>
