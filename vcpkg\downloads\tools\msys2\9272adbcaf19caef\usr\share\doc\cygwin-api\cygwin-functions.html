<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Chapter&#160;2.&#160;Cygwin Functions</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-api.html" title="Cygwin API Reference"><link rel="prev" href="std-notes.html" title="Implementation Notes"><link rel="next" href="func-cygwin-conv-path.html" title="cygwin_conv_path"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Chapter&#160;2.&#160;Cygwin Functions</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="std-notes.html">Prev</a>&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-conv-path.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h1 class="title"><a name="cygwin-functions"></a>Chapter&#160;2.&#160;Cygwin Functions</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="sect1"><a href="cygwin-functions.html#func-cygwin-path">Path conversion functions</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-conv-path.html">cygwin_conv_path</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-conv-path-list.html">cygwin_conv_path_list</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-create-path.html">cygwin_create_path</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-posix-path-list-p.html">cygwin_posix_path_list_p</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-split-path.html">cygwin_split_path</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="func-cygwin-login.html">Helper functions to change user context</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-logon_user.html">cygwin_logon_user</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-set-impersonation-token.html">cygwin_set_impersonation_token</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="func-cygwin-misc.html">Miscellaneous functions</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="func-cygwin-attach-handle-to-fd.html">cygwin_attach_handle_to_fd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-internal.html">cygwin_internal</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="func-cygwin-stackdump.html">cygwin_stackdump</a></span><span class="refpurpose"></span></dt></dl></dd></dl></div><p>
    These functions are specific to Cygwin itself, and probably won't be
    found anywhere else.
  </p><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="func-cygwin-path"></a>Path conversion functions</h2></div></div></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="std-notes.html">Prev</a>&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-conv-path.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Implementation Notes&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_conv_path</td></tr></table></div></body></html>
