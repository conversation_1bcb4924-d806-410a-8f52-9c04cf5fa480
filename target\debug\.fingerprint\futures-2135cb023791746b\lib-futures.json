{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 16971202462324950932, "deps": [[5103565458935487, "futures_io", false, 15738631877131393545], [1811549171721445101, "futures_channel", false, 11034357251717201261], [7013762810557009322, "futures_sink", false, 16339093799909363168], [7620660491849607393, "futures_core", false, 10672795016643040551], [10629569228670356391, "futures_util", false, 10146807928191515444], [12779779637805422465, "futures_executor", false, 3978430214420564327], [16240732885093539806, "futures_task", false, 5677929843572995544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-2135cb023791746b\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}