alsa-dep-fix.patch 674cc8cc421d4b9b225d229d682c2981fd10c9bce78734e162339b1cae398241
cmake 4.1.0-rc2
cxx-linkage-pkgconfig.diff af0f3c506d447f4ee39ca1bb73b398bf578f664e552273c1c54aa22aec6bed31
deps.patch d429bf68ebd9ebe26a86b1e52406caae8866b7453b8682bacb7de91d22e56d32
features core
portfile.cmake 8ae96a83ab8ab7c6defac5c832776d3cc50ff331bd5a08b7b302c982ad44f75d
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-fba57f7bbd8178e2972189b6e3b4df4913f6cc67
usage aabdfa919705cdcc06a6ed59ef2dce9a9425d32661bc498980ff521b27cf674d
vcpkg-cmake 9f68c864de34486d19ba217f7f708d26c2a91caf4c1b5bbe4b86c788d0839ae1
vcpkg-cmake-config 331025c6cff9f70d0010021a22bfe6e061ba7b2b425ee823f220d82dfd4413dc
vcpkg.json b44e87a78ff07c8cea8a36214bf7175f51bf069369d8d6c5c2de3f00b2257222
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
