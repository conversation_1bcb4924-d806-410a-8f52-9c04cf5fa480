[{"phrase": "check ram", "shell": "systeminfo | findstr Memory", "description": "Show system RAM usage"}, {"phrase": "ram usage", "shell": "systeminfo | findstr Memory", "description": "Show RAM usage"}, {"phrase": "open youtube", "url": "https://youtube.com", "description": "Open YouTube in browser"}, {"phrase": "list files", "shell": "dir", "description": "List files in current directory"}, {"phrase": "play music", "app": "wmplayer", "description": "Launch Windows Media Player"}, {"phrase": "open browser", "url": "https://google.com", "description": "Open Google in browser"}, {"phrase": "check weather", "shell": "curl -s wttr.in/", "description": "Check current weather", "parameters": ["city"]}, {"phrase": "open website", "url": "https://{site}", "description": "Open any website", "parameters": ["site"]}, {"phrase": "search google", "url": "https://google.com/search?q={query}", "description": "Search Google", "parameters": ["query"]}, {"phrase": "system info", "shell": "systeminfo", "description": "Show detailed system information"}, {"phrase": "disk space", "shell": "dir C:\\ /-c", "description": "Show disk space usage"}, {"phrase": "running processes", "shell": "tasklist", "description": "Show running processes"}, {"phrase": "network info", "shell": "ipconfig /all", "description": "Show network configuration"}]