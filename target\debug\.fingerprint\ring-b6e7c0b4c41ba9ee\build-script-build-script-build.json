{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 4665172572258138482, "deps": [[8413798824750015470, "cc", false, 9158884841973658891]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-b6e7c0b4c41ba9ee\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}