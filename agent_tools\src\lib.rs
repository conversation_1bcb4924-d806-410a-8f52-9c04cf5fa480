use std::process::Command;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum AgentAction {
    Show<PERSON>am<PERSON>sage,
    OpenBrowser(String), // URL
    PlayMusic,
    Shell(String), // Arbitrary shell command
    Url(String),   // Open any URL
    App(String),   // Launch an application
    Custom(String),// For future extension
}

#[derive(Debug)]
pub enum AgentError {
    Io(std::io::Error),
    Playwright(String),
    Unknown,
}

impl From<std::io::Error> for AgentError {
    fn from(e: std::io::Error) -> Self {
        AgentError::Io(e)
    }
}

/// Execute a system control action and return output or error
pub fn execute_action(action: AgentAction) -> Result<String, AgentError> {
    match action {
        AgentAction::ShowRamUsage => {
            // Linux/Mac: free -h, Windows: systeminfo
            #[cfg(target_os = "windows")]
            {
                let output = Command::new("systeminfo").output()?;
                Ok(String::from_utf8_lossy(&output.stdout).to_string())
            }
            #[cfg(not(target_os = "windows"))]
            {
                let output = Command::new("free").arg("-h").output()?;
                Ok(String::from_utf8_lossy(&output.stdout).to_string())
            }
        }
        AgentAction::OpenBrowser(url) | AgentAction::Url(url) => {
            // Use Playwright CLI to open Chrome and navigate to URL
            // 'playwright open <url>' opens the URL in a browser
            let output = Command::new("playwright")
                .arg("open")
                .arg(&url)
                .output();
            match output {
                Ok(out) if out.status.success() => Ok(format!("Opened browser to {url}")),
                Ok(out) => Err(AgentError::Playwright(format!(
                    "Playwright failed: {}",
                    String::from_utf8_lossy(&out.stderr)
                ))),
                Err(e) => Err(AgentError::Io(e)),
            }
        }
        AgentAction::PlayMusic => {
            // Try to launch a default media player
            #[cfg(target_os = "windows")]
            {
                let _output = Command::new("powershell").arg("-c").arg("Start-Process wmplayer").output()?;
                Ok("Launched Windows Media Player".to_string())
            }
            #[cfg(target_os = "macos")]
            {
                let output = Command::new("open").arg("-a").arg("Music").output()?;
                Ok("Launched Music app".to_string())
            }
            #[cfg(target_os = "linux")]
            {
                let output = Command::new("xdg-open").arg("/usr/bin/rhythmbox").output()?;
                Ok("Launched Rhythmbox".to_string())
            }
        }
        AgentAction::Shell(cmd) => {
            #[cfg(target_os = "windows")]
            let output = Command::new("cmd").arg("/C").arg(&cmd).output();
            #[cfg(not(target_os = "windows"))]
            let output = Command::new("sh").arg("-c").arg(&cmd).output();
            match output {
                Ok(out) if out.status.success() => Ok(String::from_utf8_lossy(&out.stdout).to_string()),
                Ok(_out) => Err(AgentError::Unknown),
                Err(e) => Err(AgentError::Io(e)),
            }
        },
        AgentAction::App(app) => {
            let output = Command::new(app).output();
            match output {
                Ok(out) if out.status.success() => Ok("Application launched".to_string()),
                Ok(_out) => Err(AgentError::Unknown),
                Err(e) => Err(AgentError::Io(e)),
            }
        },
        AgentAction::Custom(s) => Ok(format!("Custom action: {s}")),
    }
}
