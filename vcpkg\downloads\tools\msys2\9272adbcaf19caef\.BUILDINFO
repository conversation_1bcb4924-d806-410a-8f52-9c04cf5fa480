format = 2
pkgname = msys2-runtime
pkgbase = msys2-runtime
pkgver = 3.6.2-2
pkgarch = x86_64
pkgbuild_sha256sum = 964da98ba1c7071b717553a693750047bd940481c5b3b47ec54e7655fa7fc6ba
packager = CI (msys2/msys2-autobuild/5c250470/15413199460)
builddate = 1748941794
builddir = /c/S/B
startdir = /c/S/B
buildtool = makepkg
buildtoolver = 6.1.0
buildenv = !distcc
buildenv = color
buildenv = !ccache
buildenv = check
buildenv = !sign
options = strip
options = docs
options = !libtool
options = staticlibs
options = emptydirs
options = zipman
options = purge
options = !debug
options = !lto
options = !autodeps
installed = autoconf-wrapper-20240607-1-any
installed = autoconf2.13-2.13-6-any
installed = autoconf2.69-2.69-4-any
installed = autoconf2.71-2.71-4-any
installed = autoconf2.72-2.72-3-any
installed = automake-wrapper-20240607-1-any
installed = automake1.11-1.11.6-6-any
installed = automake1.12-1.12.6-6-any
installed = automake1.13-1.13.4-7-any
installed = automake1.14-1.14.1-6-any
installed = automake1.15-1.15.1-4-any
installed = automake1.16-1.16.5-1-any
installed = automake1.17-1.17-1-any
installed = autotools-2022.01.16-2-any
installed = base-2022.06-1-any
installed = base-devel-2024.11-1-any
installed = bash-5.2.037-2-x86_64
installed = bash-completion-2.16.0-1-any
installed = binutils-2.44-1-x86_64
installed = bison-3.8.2-5-x86_64
installed = brotli-1.1.0-2-x86_64
installed = bsdtar-3.8.1-1-x86_64
installed = bzip2-1.0.8-4-x86_64
installed = ca-certificates-20241223-1-any
installed = cocom-0.996-4-x86_64
installed = coreutils-8.32-5-x86_64
installed = curl-8.14.0-2-x86_64
installed = dash-0.5.12-1-x86_64
installed = db-6.2.32-5-x86_64
installed = diffstat-1.68-1-x86_64
installed = diffutils-3.12-1-x86_64
installed = docbook-xml-4.5-4-any
installed = docbook-xsl-1.79.2-3-any
installed = dos2unix-7.5.2-1-x86_64
installed = file-5.46-2-x86_64
installed = filesystem-2025.05.08-1-x86_64
installed = findutils-4.10.0-2-x86_64
installed = flex-2.6.4-4-x86_64
installed = gawk-5.3.2-1-x86_64
installed = gcc-13.3.0-1-x86_64
installed = gcc-libs-13.3.0-1-x86_64
installed = gdbm-1.25-1-x86_64
installed = getent-2.18.90-5-x86_64
installed = gettext-0.22.5-1-x86_64
installed = gettext-devel-0.22.5-1-x86_64
installed = git-2.49.0-1-x86_64
installed = gmp-6.3.0-1-x86_64
installed = gnupg-2.4.8-1-x86_64
installed = grep-1~3.0-7-x86_64
installed = gzip-1.14-1-x86_64
installed = heimdal-7.8.0-5-x86_64
installed = heimdal-libs-7.8.0-5-x86_64
installed = inetutils-2.6-1-x86_64
installed = info-7.2-1-x86_64
installed = isl-0.27-1-x86_64
installed = less-678-1-x86_64
installed = libargp-20241207-1-x86_64
installed = libasprintf-0.22.5-1-x86_64
installed = libassuan-3.0.2-1-x86_64
installed = libbz2-1.0.8-4-x86_64
installed = libcbor-0.12.0-1-x86_64
installed = libcurl-8.14.0-2-x86_64
installed = libdb-6.2.32-5-x86_64
installed = libedit-20240808_3.1-1-x86_64
installed = libexpat-2.7.1-1-x86_64
installed = libffi-3.4.8-1-x86_64
installed = libfido2-1.16.0-1-x86_64
installed = libgcrypt-1.11.1-1-x86_64
installed = libgdbm-1.25-1-x86_64
installed = libgettextpo-0.22.5-1-x86_64
installed = libgnutls-3.8.9-1-x86_64
installed = libgpg-error-1.55-1-x86_64
installed = libhogweed-3.10.1-1-x86_64
installed = libiconv-1.18-1-x86_64
installed = libiconv-devel-1.18-1-x86_64
installed = libidn2-2.3.8-1-x86_64
installed = libintl-0.22.5-1-x86_64
installed = libksba-1.6.7-1-x86_64
installed = libltdl-2.5.4-1-x86_64
installed = liblz4-1.10.0-1-x86_64
installed = liblzma-5.8.1-1-x86_64
installed = libnettle-3.10.1-1-x86_64
installed = libnghttp2-1.65.0-1-x86_64
installed = libnpth-1.8-1-x86_64
installed = libopenssl-3.5.0-1-x86_64
installed = libp11-kit-0.25.5-2-x86_64
installed = libpcre-8.45-5-x86_64
installed = libpcre2_8-10.45-1-x86_64
installed = libpsl-0.21.5-2-x86_64
installed = libreadline-8.2.013-1-x86_64
installed = libsqlite-3.50.0-1-x86_64
installed = libssh2-1.11.1-1-x86_64
installed = libtasn1-4.20.0-1-x86_64
installed = libtool-2.5.4-1-x86_64
installed = libunistring-1.3-1-x86_64
installed = libutil-linux-2.40.2-2-x86_64
installed = libxcrypt-4.4.38-1-x86_64
installed = libxml2-2.13.8-1-x86_64
installed = libxslt-1.1.43-1-x86_64
installed = libzstd-1.5.7-1-x86_64
installed = m4-1.4.19-2-x86_64
installed = make-4.4.1-2-x86_64
installed = mingw-w64-cross-common-binutils-2.44-1-x86_64
installed = mingw-w64-cross-crt-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-gcc-15.1.0-1-x86_64
installed = mingw-w64-cross-mingw32-binutils-2.44-1-x86_64
installed = mingw-w64-cross-mingw32-crt-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw32-gcc-15.1.0-1-x86_64
installed = mingw-w64-cross-mingw32-headers-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw32-windows-default-manifest-6.4-5-x86_64
installed = mingw-w64-cross-mingw32-winpthreads-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw32-zlib-1.3.1-4-x86_64
installed = mingw-w64-cross-mingw64-binutils-2.44-1-x86_64
installed = mingw-w64-cross-mingw64-crt-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw64-gcc-15.1.0-1-x86_64
installed = mingw-w64-cross-mingw64-headers-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw64-windows-default-manifest-6.4-5-x86_64
installed = mingw-w64-cross-mingw64-winpthreads-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-mingw64-zlib-1.3.1-4-x86_64
installed = mingw-w64-cross-ucrt64-binutils-2.44-1-x86_64
installed = mingw-w64-cross-ucrt64-crt-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-ucrt64-gcc-15.1.0-1-x86_64
installed = mingw-w64-cross-ucrt64-headers-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-ucrt64-windows-default-manifest-6.4-5-x86_64
installed = mingw-w64-cross-ucrt64-winpthreads-12.0.0.r731.g2ca6f1348-1-x86_64
installed = mingw-w64-cross-ucrt64-zlib-1.3.1-4-x86_64
installed = mingw-w64-cross-zlib-1.3.1-4-x86_64
installed = mintty-1~3.7.8-1-x86_64
installed = mpc-1.3.1-1-x86_64
installed = mpfr-4.2.2-1-x86_64
installed = msys2-keyring-1~20250214-1-any
installed = msys2-launcher-1.5-3-x86_64
installed = msys2-runtime-3.6.2-1-x86_64
installed = msys2-runtime-devel-3.6.2-1-x86_64
installed = msys2-w32api-headers-12.0.0.r0.g819a6ec2e-2-x86_64
installed = msys2-w32api-runtime-12.0.0.r0.g819a6ec2e-2-x86_64
installed = nano-8.4-1-x86_64
installed = ncurses-6.5.20240831-2-x86_64
installed = nettle-3.10.1-1-x86_64
installed = openssh-10.0p1-2-x86_64
installed = openssl-3.5.0-1-x86_64
installed = p11-kit-0.25.5-2-x86_64
installed = pacman-6.1.0-14-x86_64
installed = pacman-contrib-1.10.6-1-x86_64
installed = pacman-mirrors-20250220-1-any
installed = patch-2.7.6-3-x86_64
installed = perl-5.38.4-2-x86_64
installed = perl-Authen-SASL-2.1800-1-any
installed = perl-Clone-0.47-1-x86_64
installed = perl-Convert-BinHex-1.125-2-any
installed = perl-Encode-Locale-1.05-2-any
installed = perl-Error-0.17030-1-any
installed = perl-File-Listing-6.16-1-any
installed = perl-HTML-Parser-3.83-1-x86_64
installed = perl-HTML-Tagset-3.24-1-any
installed = perl-HTTP-Cookies-6.11-1-any
installed = perl-HTTP-Daemon-6.16-1-any
installed = perl-HTTP-Date-6.06-1-any
installed = perl-HTTP-Message-7.00-1-any
installed = perl-HTTP-Negotiate-6.01-3-any
installed = perl-IO-HTML-1.004-2-any
installed = perl-IO-Socket-SSL-2.089-1-any
installed = perl-IO-Stringy-2.113-2-any
installed = perl-LWP-MediaTypes-6.04-2-any
installed = perl-MIME-tools-5.515-1-any
installed = perl-MailTools-2.22-1-any
installed = perl-Module-Build-0.4234-2-any
installed = perl-Net-HTTP-6.23-1-any
installed = perl-Net-SMTP-SSL-1.04-2-any
installed = perl-Net-SSLeay-1.94-2-x86_64
installed = perl-TermReadKey-2.38-6-x86_64
installed = perl-Test-Pod-1.52-2-any
installed = perl-TimeDate-2.33-2-any
installed = perl-Try-Tiny-0.32-1-any
installed = perl-URI-5.31-1-any
installed = perl-WWW-RobotRules-6.02-3-any
installed = perl-YAML-Syck-1.34-4-x86_64
installed = perl-http-cookiejar-0.014-1-any
installed = perl-inc-latest-0.500-2-any
installed = perl-libwww-6.78-1-any
installed = pinentry-1.3.1-2-x86_64
installed = pkgconf-2.4.3-1-x86_64
installed = rebase-4.5.0-5-x86_64
installed = sed-4.9-1-x86_64
installed = tar-1.35-2-x86_64
installed = texinfo-7.2-1-x86_64
installed = texinfo-tex-7.2-1-x86_64
installed = time-1.9-3-x86_64
installed = tzcode-2025b-1-x86_64
installed = util-linux-2.40.2-2-x86_64
installed = wget-1.25.0-1-x86_64
installed = which-2.23-4-x86_64
installed = windows-default-manifest-6.4-2-x86_64
installed = xmlto-0.0.29-1-x86_64
installed = xz-5.8.1-1-x86_64
installed = zlib-1.3.1-1-x86_64
installed = zlib-devel-1.3.1-1-x86_64
installed = zstd-1.5.7-1-x86_64
