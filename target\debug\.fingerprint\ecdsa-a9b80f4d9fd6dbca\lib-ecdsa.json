{"rustc": 1842507548689473721, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 2241668132362809309, "path": 17760564894305177630, "deps": [[4234225094004207019, "rfc6979", false, 15096172565992330925], [10149501514950982522, "elliptic_curve", false, 4912730669152879216], [10800937535932116261, "der", false, 8614210689495419054], [11285023886693207100, "spki", false, 7301719680771759613], [13895928991373641935, "signature", false, 6601972649289187882], [17475753849556516473, "digest", false, 8654851830395125740]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ecdsa-a9b80f4d9fd6dbca\\dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}