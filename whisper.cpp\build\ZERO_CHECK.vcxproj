﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4C435E7B-E811-3F09-974A-C0840C54E5D9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\6419dfc4d5a91cbb779f322da9394a31\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/whisper.cpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTest.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestTargets.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestUseLaunchers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\DartConfiguration.tcl.in;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Templates\CTestScript.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\bindings\javascript\package-tmpl.json;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\build-info.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\git-vars.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper.pc.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\bench\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\cli\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\command\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\deprecation-warning\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\lsp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\quantize\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\server\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\stream\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\vad-speech-segments\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\libwchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\wchess.cmd\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\common.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\tests\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\cli\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\bench\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\server\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\quantize\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\vad-speech-segments\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\stream\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\command\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\talk-llama\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\lsp\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\deprecation-warning\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\libwchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\wchess.cmd\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/whisper.cpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTest.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestTargets.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestUseLaunchers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\DartConfiguration.tcl.in;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Templates\CTestScript.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\bindings\javascript\package-tmpl.json;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\build-info.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\git-vars.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper.pc.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\bench\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\cli\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\command\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\deprecation-warning\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\lsp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\quantize\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\server\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\stream\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\vad-speech-segments\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\libwchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\wchess.cmd\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\common.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\tests\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\cli\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\bench\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\server\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\quantize\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\vad-speech-segments\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\stream\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\command\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\talk-llama\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\lsp\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\deprecation-warning\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\libwchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\wchess.cmd\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/whisper.cpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTest.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestTargets.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestUseLaunchers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\DartConfiguration.tcl.in;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Templates\CTestScript.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\bindings\javascript\package-tmpl.json;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\build-info.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\git-vars.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper.pc.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\bench\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\cli\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\command\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\deprecation-warning\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\lsp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\quantize\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\server\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\stream\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\vad-speech-segments\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\libwchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\wchess.cmd\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\common.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\tests\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\cli\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\bench\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\server\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\quantize\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\vad-speech-segments\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\stream\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\command\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\talk-llama\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\lsp\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\deprecation-warning\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\libwchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\wchess.cmd\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp -BC:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/whisper.cpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-4.1\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTest.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestTargets.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CTestUseLaunchers.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\DartConfiguration.tcl.in;C:\Program Files\CMake\share\cmake-4.1\Modules\FeatureSummary.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindOpenMP.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-4.1\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\CMake\share\cmake-4.1\Templates\CTestScript.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\bindings\javascript\package-tmpl.json;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\4.1.0-rc2\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\DefaultTargetOptions.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\build-info.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\git-vars.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\cmake\whisper.pc.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\bench\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\cli\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\command\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\deprecation-warning\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\lsp\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\quantize\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\server\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\stream\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\vad-speech-segments\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\libwchess\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\wchess\wchess.cmd\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\common.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\cmake\ggml-config.cmake.in;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\ggml\src\ggml-cpu\cmake\FindSIMD.cmake;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\src\CMakeLists.txt;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\ggml\src\ggml-cpu\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\src\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\tests\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\cli\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\bench\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\server\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\quantize\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\vad-speech-segments\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\stream\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\command\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\talk-llama\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\lsp\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\deprecation-warning\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\libwchess\CMakeFiles\generate.stamp;C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\build\examples\wchess\wchess.cmd\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>