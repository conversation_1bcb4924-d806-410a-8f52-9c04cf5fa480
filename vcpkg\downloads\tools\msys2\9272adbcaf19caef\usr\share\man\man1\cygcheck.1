'\" t
.\"     Title: cygcheck
.\"    Author: [FIXME: author] [see http://www.docbook.org/tdg5/en/html/author]
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "CYGCHECK" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
cygcheck \- List system information, check installed packages, or query package database
.SH "SYNOPSIS"
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR [\-v] [\-h] \fIPROGRAM\fR
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-c [\-d] [\-n] [\fIPACKAGE\fR]
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-s [\-r] [\-v] [\-h]
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-k
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-e [\-\-requires] [\-\-build\-reqs] \fIPATTERN\fR...
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-i [\-\-inst] [\-\-curr] [\-\-prev] [\-\-test] [\-\-deps] [\-\-build\-deps] \fIPATTERN\fR...
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-f \fIFILE\fR...
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-l [\fIPACKAGE\fR...]
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-p\ \fIREGEXP\fR
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-\-delete\-orphaned\-installation\-keys
.HP \w'\fBcygcheck\fR\ 'u
\fBcygcheck\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
At least one command option or a PROGRAM is required, as shown above\&.

  PROGRAM              list library (DLL) dependencies of PROGRAM
  \-c, \-\-check\-setup    show installed version of PACKAGE and verify integrity
                       (or for all installed packages if none specified)
  \-d, \-\-dump\-only      do not verify packages (with \-c)
  \-n, \-\-names\-only     just list package names (implies \-c \-d)
  \-s, \-\-sysinfo        produce diagnostic system information (implies \-c \-d)
  \-r, \-\-registry       also scan registry for Cygwin settings (with \-s)
  \-k, \-\-keycheck       perform a keyboard check session (must be run from a
                       plain console only, not from a pty/rxvt/xterm)
  \-e, \-\-search\-package list all available packages matching PATTERN
                       PATTERN is a glob pattern with * and ? as wildcard chars
      search selection specifiers (multiple allowed):
      \-\-requires       list packages depending on packages matching PATTERN
      \-\-build\-reqs     list packages depending on packages matching PATTERN
                       when building these packages
                       only the most recent available releases are checked
                       to collect requirements info
  \-i, \-\-info\-package   print full info on packages matching PATTERN, installed
                       and available releases
                       PATTERN is a glob pattern with * and ? as wildcard chars
      info selection specifiers (multiple allowed):
      \-\-inst           only print info on installed package release
      \-\-curr           only print info on most recent available release
      \-\-prev           only print info on older, still available releases
      \-\-test           only print info on test releases
      \-\-deps           additionally print package dependencies
      \-\-build\-deps     additionally print package build dependencies
  \-f, \-\-find\-package   find the package to which FILE belongs
  \-l, \-\-list\-package   list contents of PACKAGE (or all packages if none given)
  \-p, \-\-package\-query  search for REGEXP in the entire cygwin\&.com package
                       repository (requires internet connectivity)
  \-\-delete\-orphaned\-installation\-keys
                       Delete installation keys of old, now unused
                       installations from the registry\&.  Requires the right
                       to change the registry\&.
  \-v, \-\-verbose        produce more verbose output
  \-h, \-\-help           annotate output with explanatory comments when given
                       with another command, otherwise print this help
  \-V, \-\-version        print the version of cygcheck and exit

Notes:
  \-c, \-f, and \-l only report on packages that are currently installed\&.
  \-i and \-e report on available packages, too\&.  To search for files within
  uninstalled Cygwin packages, use \-p\&.  The \-p REGEXP matches package names,
  descriptions, and names of files/paths within all packages\&.
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The
\fBcygcheck\fR
program is a diagnostic utility for dealing with Cygwin programs\&. If you are familiar with
\fBdpkg\fR
or
\fBrpm\fR,
\fBcygcheck\fR
is similar in many ways\&. (The major difference is that
\fBsetup\fR
handles installing and uninstalling packages; see
the section called \(lqInternet Setup\(rq
for more information\&.)
.PP
The
\-c
option checks the version and status of installed Cygwin packages\&. If you specify one or more package names,
\fBcygcheck\fR
will limit its output to those packages, or with no arguments it lists all packages\&. A package will be marked
Incomplete
if files originally installed are no longer present\&. The best thing to do in that situation is reinstall the package with
\fBsetup\fR\&. To see which files are missing, use the
\-v
option\&. If you do not need to know the status of each package and want
\fBcygcheck\fR
to run faster, add the
\-d
option and
\fBcygcheck\fR
will only output the name and version for each package\&. Add the
\-n
option to output only the names of packages\&.
.PP
If you list one or more programs on the command line,
\fBcygcheck\fR
will diagnose the runtime environment of that program or programs, providing the names of DLL files on which the program depends\&. If you specify the
\-s
option,
\fBcygcheck\fR
will give general system information\&. If you list one or more programs on the command line and specify
\-s,
\fBcygcheck\fR
will report on both\&.
.PP
The
\-e
option allows to seach for available packages in the Cygwin distribution\&.
PATTERN
is a glob pattern, using * and ? as wildcard characters, just as in filename patterns\&.
PATTERN
is searched for in the package name and the summary of a package\&. The
\-\-requires
and
\-\-build\-reqs
options allow to search for packages which have a certain dependency, either at runtime or at build time\&.
.PP
The
\-i
option prints a lot of information available for installed packages, as well as for available packages in the Cygwin distribution\&.
PATTERN
is a glob pattern, using * and ? as wildcard characters, just as in filename patterns\&.
PATTERN
is compared against the package name as well as against the combined package name and version\&. With additional info selectors,
\-\-inst,
\-\-curr,
\-\-prev, and
\-\-test, allow to specify that only information in terms of installed, current latest available, older available, as well as test packages respectively, is requested\&. The
\-\-deps
and
\-\-build\-deps
options allow to print additional dependency information\&.
.PP
\fBExample\ \&3.4.\ \&Example cygcheck \-e/\-i usage\fR
.sp
.if n \{\
.RS 4
.\}
.nf
$ cygcheck \-e grep
grep : search for regular expression matches in text files
grep\-debuginfo : Debug info for grep
grepmail : search mailboxes for mail matching an expression
pdfgrep : Command\-line utility for searching text in PDFs
pdfgrep\-debuginfo : Debug info for pdfgrep
sgrep : Search indexed text regions like SGML,XML and HTML files

$ cygcheck \-i \-\-curr \-\-deps grep
Latest available package:
\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-

Name        : grep
Version     : 3\&.8
Release     : 2
Architecture: x86_64
Size        : 401340 (392 K)
Source      : grep\-3\&.8\-2\-src\&.tar\&.xz
Dependencies: bash, cygwin, libintl8, libpcre2_8_0
Summary     : search for regular expression matches in text files
Description :
GNU grep searches one or more input files for lines containing a
match to a specified pattern\&. By default, grep outputs the matching lines\&.
The GNU implementation includes several useful extensions over POSIX\&.

.fi
.if n \{\
.RE
.\}
.PP
Note that
\-e
and
\-i
options fetch info from a distribution db file\&. This file will be downloading on demand and refreshed if it\*(Aqs older than 24 hours\&.
.PP
The
\-f
option helps you to track down which package a file came from, and
\-l
lists all files in a package\&. For example, to find out about
/usr/bin/less
and its package:
.PP
\fBExample\ \&3.5.\ \&Example cygcheck \-f/\-l usage\fR
.sp
.if n \{\
.RS 4
.\}
.nf
$ cygcheck \-f /usr/bin/less
less\-381\-1

$ cygcheck \-l less
/usr/bin/less\&.exe
/usr/bin/lessecho\&.exe
/usr/bin/lesskey\&.exe
/usr/man/man1/less\&.1
/usr/man/man1/lesskey\&.1
.fi
.if n \{\
.RE
.\}
.PP
The
\-h
option prints additional helpful messages in the report, at the beginning of each section\&. It also adds table column headings\&. While this is useful information, it also adds some to the size of the report, so if you want a compact report or if you know what everything is already, just leave this out\&.
.PP
The
\-v
option causes the output to be more verbose\&. What this means is that additional information will be reported which is usually not interesting, such as the internal version numbers of DLLs, additional information about recursive DLL usage, and if a file in one directory in the PATH also occurs in other directories on the PATH\&.
.PP
The
\-r
option causes
\fBcygcheck\fR
to search your registry for information that is relevant to Cygwin programs\&. These registry entries are the ones that have "Cygwin" in the name\&. If you are paranoid about privacy, you may remove information from this report, but please keep in mind that doing so makes it harder to diagnose your problems\&.
.PP
In contrast to the other options that search the packages that are installed on your local system, the
\-p
option can be used to search the entire official Cygwin package repository\&. It takes as argument a Perl\-compatible regular expression which is used to match package names, package descriptions, and path/filenames of the contents of packages\&. This feature requires an active internet connection, since it must query the
cygwin\&.com
web site\&. In fact, it is equivalent to the search that is available on the
\m[blue]\fBCygwin package listing\fR\m[]\&\s-2\u[1]\d\s+2
page\&.
.PP
For example, perhaps you are getting an error because you are missing a certain DLL and you want to know which package includes that file:
.PP
\fBExample\ \&3.6.\ \&Searching all packages for a file\fR
.sp
.if n \{\
.RS 4
.\}
.nf
$ cygcheck \-p \*(Aqcygintl\-2\e\&.dll\*(Aq
Found 1 matches for \*(Aqcygintl\-2\e\&.dll\*(Aq\&.

libintl2\-0\&.12\&.1\-3         GNU Internationalization runtime library

$ cygcheck \-p \*(Aqlibexpat\&.*\e\&.a\*(Aq
Found 2 matches for \*(Aqlibexpat\&.*\e\&.a\*(Aq\&.

expat\-1\&.95\&.7\-1            XML parser library written in C
expat\-1\&.95\&.8\-1            XML parser library written in C

$ cygcheck \-p \*(Aq/ls\e\&.exe\*(Aq
Found 2 matches for \*(Aq/ls\e\&.exe\*(Aq\&.

coreutils\-5\&.2\&.1\-5         GNU core utilities (includes fileutils, sh\-utils and textutils)
coreutils\-5\&.3\&.0\-6         GNU core utilities (includes fileutils, sh\-utils and textutils)
.fi
.if n \{\
.RE
.\}
.PP
Note that this option takes a regular expression, not a glob or wildcard\&. This means that you need to use
\&.*
if you want something similar to the wildcard
*
commonly used in filename globbing\&. Similarly, to match the period character you should use
\e\&.
since the
\&.
character in a regexp is a metacharacter that will match any character\&. Also be aware that the characters such as
\e
and
*
are shell metacharacters, so they must be either escaped or quoted, as in the example above\&.
.PP
The third example above illustrates that if you want to match a whole filename, you should include the
/
path seperator\&. In the given example this ensures that filenames that happen to end in
ls\&.exe
such as
ncftpls\&.exe
are not shown\&. Note that this use does not mean "look for packages with
ls
in the root directory," since the
/
can match anywhere in the path\&. It\*(Aqs just there to anchor the match so that it matches a full filename\&.
.PP
By default the matching is case\-sensitive\&. To get a case insensitive match, begin your regexp with
(?i)
which is a PCRE\-specific feature\&. For complete documentation on Perl\-compatible regular expression syntax and options, read the
\fBperlre\fR
manpage, or one of many websites such as
perldoc\&.com
that document the Perl language\&.
.PP
The
\fBcygcheck\fR
program should be used to send information about your system for troubleshooting when requested\&. When asked to run this command save the output so that you can email it, for example:
.sp
.if n \{\
.RS 4
.\}
.nf
$ \fBcygcheck \-s \-v \-r \-h > cygcheck_output\&.txt\fR
.fi
.if n \{\
.RE
.\}
.PP
Each Cygwin DLL stores its path and installation key in the registry\&. This allows troubleshooting of problems which could be a result of having multiple concurrent Cygwin installations\&. However, if you\*(Aqre experimenting a lot with different Cygwin installation paths, your registry could accumulate a lot of old Cygwin installation entries for which the installation doesn\*(Aqt exist anymore\&. To get rid of these orphaned registry entries, use the
\fBcygcheck \-\-delete\-orphaned\-installation\-keys\fR
command\&.
.SH "COPYRIGHT"
.br
.PP
Copyright \(co Cygwin authors
.PP
Permission is granted to make and distribute verbatim copies of this documentation provided the copyright notice and this permission notice are preserved on all copies.
.PP
Permission is granted to copy and distribute modified versions of this documentation under the conditions for verbatim copying, provided that the entire resulting derived work is distributed under the terms of a permission notice identical to this one.
.PP
Permission is granted to copy and distribute translations of this documentation into another language, under the above conditions for modified versions, except that this permission notice may be stated in a translation approved by the Free Software Foundation.
.sp
.SH "NOTES"
.IP " 1." 4
Cygwin package listing
.RS 4
\%https://cygwin.com/packages/
.RE
