{"rustc": 1842507548689473721, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 15657897354478470176, "path": 1043756640006038668, "deps": [[1999565553139417705, "windows_sys", false, 13064610496217613790], [3869670940427635694, "filetime", false, 17948626117272107875], [4684437522915235464, "libc", false, 3727455428662936598], [5986029879202738730, "log", false, 13745748516531157483], [9727213718512686088, "crossbeam_channel", false, 17956018497612537386], [15622660310229662834, "walkdir", false, 4450891707465107434]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-f88435c2d40394ef\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}