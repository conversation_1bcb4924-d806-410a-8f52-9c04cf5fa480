'\" t
.\"     Title: ssp
.\"    Author: <PERSON>
.\" Generator: DocBook XSL Stylesheets vsnapshot <http://docbook.sf.net/>
.\"      Date: 06/03/2025
.\"    Manual: Cygwin Utilities
.\"    Source: Cygwin Utilities
.\"  Language: English
.\"
.TH "SSP" "1" "06/03/2025" "Cygwin Utilities" "Cygwin Utilities"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
ssp \- The Single Step Profiler
.SH "SYNOPSIS"
.HP \w'\fBssp\fR\ 'u
\fBssp\fR [\-cdelstv] \fIlow_pc\fR \fIhigh_pc\fR \fIcommand\fR...
.HP \w'\fBssp\fR\ 'u
\fBssp\fR \-h | \-V 
.SH "OPTIONS"
.sp
.if n \{\
.RS 4
.\}
.nf
 \-c, \-\-console\-trace  trace every EIP value to the console\&. *Lots* slower\&.
 \-d, \-\-disable        disable single\-stepping by default; use
                      OutputDebugString ("ssp on") to enable stepping
 \-e, \-\-enable         enable single\-stepping by default; use
                      OutputDebugString ("ssp off") to disable stepping
 \-h, \-\-help           output usage information and exit
 \-l, \-\-dll            enable dll profiling\&.  A chart of relative DLL usage
                      is produced after the run\&.
 \-s, \-\-sub\-threads    trace sub\-threads too\&.  Dangerous if you have
                      race conditions\&.
 \-t, \-\-trace\-eip      trace every EIP value to a file TRACE\&.SSP\&.  This
                      gets big *fast*\&.
 \-v, \-\-verbose        output verbose messages about debug events\&.
 \-V, \-\-version        output version information and exit

Example: ssp 0x401000 0x403000 hello\&.exe
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The SSP is a program that uses the Win32 debug API to run a program one ASM instruction at a time\&. It records the location of each instruction used, how many times that instruction is used, and all function calls\&. The results are saved in a format that is usable by the profiling program
\fBgprof\fR, although
\fBgprof\fR
will claim the values are seconds, they really are instruction counts\&. More on that later\&.
.PP
(See also
\fBprofiler\fR, another profiler that operates in a different fashion: IP sampling\&. This can provide a different view on your program\*(Aqs operation\&.)
.PP
Because the SSP was originally designed to profile the Cygwin DLL, it does not automatically select a block of code to report statistics on\&. You must specify the range of memory addresses to keep track of manually, but it\*(Aqs not hard to figure out what to specify\&. Use the "objdump" program to determine the bounds of the target\*(Aqs "\&.text" section\&. Let\*(Aqs say we\*(Aqre profiling cygwin1\&.dll\&. Make sure you\*(Aqve built it with debug symbols (else
\fBgprof\fR
won\*(Aqt run) and run objdump like this:
.sp
.if n \{\
.RS 4
.\}
.nf
$ objdump \-h cygwin1\&.dll
.fi
.if n \{\
.RE
.\}
.sp
It will print a report like this:
.sp
.if n \{\
.RS 4
.\}
.nf
cygwin1\&.dll:     file format pei\-i386

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 \&.text         0007ea00  61001000  61001000  00000400  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE, DATA
  1 \&.data         00008000  61080000  61080000  0007ee00  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  \&. \&. \&.
.fi
.if n \{\
.RE
.\}
.PP
The only information we\*(Aqre concerned with are the VMA of the \&.text section and the VMA of the section after it (sections are usually contiguous; you can also add the Size to the VMA to get the end address)\&. In this case, the VMA is 0x61001000 and the ending address is either 0x61080000 (start of \&.data method) or 0x6107fa00 (VMA+Size method)\&.
.PP
There are two basic ways to use SSP \- either profiling a whole program, or selectively profiling parts of the program\&.
.PP
To profile a whole program, just run
\fBssp\fR
without options\&. By default, it will step the whole program\&. Here\*(Aqs a simple example, using the numbers above:
.sp
.if n \{\
.RS 4
.\}
.nf
$ ssp 0x61001000 0x61080000 hello\&.exe
.fi
.if n \{\
.RE
.\}
.sp
This will step the whole program\&. It will take at least 8 minutes on a PII/300 (yes, really)\&. When it\*(Aqs done, it will create a file called "gmon\&.out"\&. You can turn this data file into a readable report with
\fBgprof\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
$ gprof \-b cygwin1\&.dll
.fi
.if n \{\
.RE
.\}
.sp
The "\-b" means \*(Aqskip the help pages\*(Aq\&. You can omit this until you\*(Aqre familiar with the report layout\&. The
\fBgprof\fR
documentation explains a lot about this report, but
\fBssp\fR
changes a few things\&. For example, the first part of the report reports the amount of time spent in each function, like this:
.sp
.if n \{\
.RS 4
.\}
.nf
Each sample counts as 0\&.01 seconds\&.
  %   cumulative   self              self     total
 time   seconds   seconds    calls  ms/call  ms/call  name
 10\&.02    231\&.22    72\&.43       46  1574\&.57  1574\&.57  strcspn
  7\&.95    288\&.70    57\&.48      130   442\&.15   442\&.15  strncasematch
.fi
.if n \{\
.RE
.\}
.sp
The "seconds" columns are really CPU opcodes, 1/100 second per opcode\&. So, "231\&.22" above means 23,122 opcodes\&. The ms/call values are 10x too big; 1574\&.57 means 157\&.457 opcodes per call\&. Similar adjustments need to be made for the "self" and "children" columns in the second part of the report\&.
.PP
OK, so now we\*(Aqve got a huge report that took a long time to generate, and we\*(Aqve identified a spot we want to work on optimizing\&. Let\*(Aqs say it\*(Aqs the time() function\&. We can use SSP to selectively profile this function by using OutputDebugString() to control SSP from within the program\&. Here\*(Aqs a sample program:
.sp
.if n \{\
.RS 4
.\}
.nf
	#include <windows\&.h>
	main()
	{
	  time_t t;
	  OutputDebugString("ssp on");
	  time(&t);
	  OutputDebugString("ssp off");
	}
.fi
.if n \{\
.RE
.\}
.PP
Then, add the
\-d
option to ssp to default to *disabling* profiling\&. The program will run at full speed until the first OutputDebugString, then step until the second\&. You can then use
\fBgprof\fR
(as usual) to see the performance profile for just that portion of the program\*(Aqs execution\&.
.PP
There are many options to ssp\&. Since step\-profiling makes your program run about 1,000 times slower than normal, it\*(Aqs best to understand all the options so that you can narrow down the parts of your program you need to single\-step\&.
.PP
\-v
\- verbose\&. This prints messages about threads starting and stopping, OutputDebugString calls, DLLs loading, etc\&.
.PP
\-t
and
\-c
\- tracing\&. With
\-t, *every* step\*(Aqs address is written to the file "trace\&.ssp"\&. This can be used to help debug functions, since it can trace multiple threads\&. Clever use of scripts can match addresses with disassembled opcodes if needed\&. Warning: creates *huge* files, very quickly\&.
\-c
prints each address to the console, useful for debugging key chunks of assembler\&. Use
addr2line \-C \-f \-s \-e foo\&.exe < trace\&.ssp > lines\&.ssp
and then
perl cvttrace
to convert to symbolic traces\&.
.PP
\-s
\- subthreads\&. Usually, you only need to trace the main thread, but sometimes you need to trace all threads, so this enables that\&. It\*(Aqs also needed when you want to profile a function that only a subthread calls\&. However, using OutputDebugString automatically enables profiling on the thread that called it, not the main thread\&.
.PP
\-l
\- dll profiling\&. Generates a pretty table of how much time was spent in each dll the program used\&. No sense optimizing a function in your program if most of the time is spent in the DLL\&. I usually use the
\-v,
\-s, and
\-l
options:
.sp
.if n \{\
.RS 4
.\}
.nf
$ ssp \-v \-s \-l \-d 0x61001000 0x61080000 hello\&.exe
.fi
.if n \{\
.RE
.\}
.sp
.SH "AUTHOR"
.PP
\fBDJ Delorie\fR
.RS 4
Original Author
.RE
.SH "COPYRIGHT"
.br
