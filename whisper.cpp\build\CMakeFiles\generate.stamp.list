C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/ggml-cpu/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/src/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cli/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/bench/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/server/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/quantize/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/vad-speech-segments/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/stream/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/command/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/talk-llama/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/lsp/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/libwchess/CMakeFiles/generate.stamp
C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/wchess.cmd/CMakeFiles/generate.stamp
