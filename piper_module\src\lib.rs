use std::fs;
use std::io::Write;
use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};
use tempfile::NamedTempFile;
use rodio::{Decoder, OutputStream, Sink};
use std::fs::File;


const PIPER_MODEL_URL: &str = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/low/en_US-lessac-low.onnx?download=true";
const PIPER_CONFIG_URL: &str = "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/low/en_US-lessac-low.onnx.json?download=true";
const MODEL_DIR: &str = "models";
const MODEL_FILE: &str = "en_US-lessac-low.onnx";
const CONFIG_FILE: &str = "en_US-lessac-low.onnx.json";
const PIPER_BIN: &str = "piper/piper.exe"; // Local piper installation

#[derive(Debug)]
pub enum PiperError {
    Io(std::io::Error),
    Download(reqwest::Error),
    PiperNotFound,
    PiperFailed(String),
    AudioPlay(String),
}

impl From<std::io::Error> for PiperError {
    fn from(e: std::io::Error) -> Self {
        PiperError::Io(e)
    }
}
impl From<reqwest::Error> for PiperError {
    fn from(e: reqwest::Error) -> Self {
        PiperError::Download(e)
    }
}

fn ensure_model_files() -> Result<(PathBuf, PathBuf), PiperError> {
    fs::create_dir_all(MODEL_DIR)?;
    let model_path = Path::new(MODEL_DIR).join(MODEL_FILE);
    let config_path = Path::new(MODEL_DIR).join(CONFIG_FILE);
    if !model_path.exists() {
        let mut resp = reqwest::blocking::get(PIPER_MODEL_URL)?;
        let mut file = File::create(&model_path)?;
        resp.copy_to(&mut file)?;
    }
    if !config_path.exists() {
        let mut resp = reqwest::blocking::get(PIPER_CONFIG_URL)?;
        let mut file = File::create(&config_path)?;
        resp.copy_to(&mut file)?;
    }
    Ok((model_path, config_path))
}

/// Speak the given text using Piper TTS and play it through the default audio device.
/// Falls back to Windows SAPI if Piper is not available.
pub fn speak(text: &str) -> Result<(), PiperError> {
    println!("🔊 TTS: {}", text); // Debug output

    // Try Piper first, fall back to Windows TTS
    match speak_with_piper(text) {
        Ok(()) => {
            println!("✅ Piper TTS successful");
            Ok(())
        },
        Err(e) => {
            println!("⚠️ Piper failed ({:?}), using Windows TTS", e);
            // Fallback to Windows SAPI TTS
            speak_with_windows_tts(text)
        }
    }
}

/// Speak using Windows built-in TTS (SAPI)
fn speak_with_windows_tts(text: &str) -> Result<(), PiperError> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        let script = format!(
            r#"Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.Speak("{}")"#,
            text.replace('"', "\"\"") // Escape quotes
        );

        let output = Command::new("powershell")
            .arg("-Command")
            .arg(&script)
            .output()
            .map_err(|e| PiperError::Io(e))?;

        if output.status.success() {
            Ok(())
        } else {
            Err(PiperError::PiperFailed("Windows TTS failed".to_string()))
        }
    }
    #[cfg(not(target_os = "windows"))]
    {
        Err(PiperError::PiperNotFound)
    }
}

/// Original Piper TTS implementation
fn speak_with_piper(text: &str) -> Result<(), PiperError> {
    let (model_path, _config_path) = ensure_model_files()?;
    // Create a temp WAV file for Piper output
    let wav_file = NamedTempFile::new()?;
    let wav_path = wav_file.path().to_path_buf();
    // Call Piper CLI
    let mut child = Command::new(PIPER_BIN)
        .arg("--model").arg(&model_path)
        .arg("--output_file").arg(&wav_path)
        .stdin(Stdio::piped())
        .stdout(Stdio::null())
        .stderr(Stdio::piped())
        .spawn()
        .map_err(|_| PiperError::PiperNotFound)?;
    {
        let stdin = child.stdin.as_mut().ok_or(PiperError::PiperFailed("Failed to open Piper stdin".to_string()))?;
        stdin.write_all(text.as_bytes())?;
    }
    let output = child.wait_with_output()?;
    if !output.status.success() {
        let err = String::from_utf8_lossy(&output.stderr).to_string();
        return Err(PiperError::PiperFailed(err));
    }
    // Play the WAV file with rodio
    if let Ok((_stream, stream_handle)) = OutputStream::try_default() {
        if let Ok(sink) = Sink::try_new(&stream_handle) {
            let file = File::open(&wav_path)?;
            if let Ok(source) = Decoder::new(std::io::BufReader::new(file)) {
                sink.append(source);
                sink.sleep_until_end();
                // Clean up temp file
                drop(wav_file);
                return Ok(());
            }
        }
    }
    // Fallback: try to play with system player
    #[cfg(target_os = "windows")]
    {
        Command::new("powershell")
            .arg("-c")
            .arg(format!("(New-Object Media.SoundPlayer '{}').PlaySync();", wav_path.display()))
            .status()
            .map_err(|e| PiperError::AudioPlay(format!("Failed to play audio: {e}")))?;
    }
    #[cfg(target_os = "linux")]
    {
        Command::new("aplay")
            .arg(&wav_path)
            .status()
            .map_err(|e| PiperError::AudioPlay(format!("Failed to play audio: {e}")))?;
    }
    #[cfg(target_os = "macos")]
    {
        Command::new("afplay")
            .arg(&wav_path)
            .status()
            .map_err(|e| PiperError::AudioPlay(format!("Failed to play audio: {e}")))?;
    }
    // Clean up temp file
    drop(wav_file);
    Ok(())
}

pub fn add(left: u64, right: u64) -> u64 {
    left + right
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let result = add(2, 2);
        assert_eq!(result, 4);
    }
}
