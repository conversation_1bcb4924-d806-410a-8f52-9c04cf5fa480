Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel'

Run Build Command(s): C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/downloads/tools/ninja/1.12.1-windows/ninja.exe -v -v -j9 install
[1/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL.h"
[2/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_assert.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_assert.h"
[3/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_atomic.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_atomic.h"
[4/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_blendmode.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_blendmode.h"
[5/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_audio.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_audio.h"
[6/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_bits.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_bits.h"
[7/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_copying.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_copying.h"
[8/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_clipboard.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_clipboard.h"
[9/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_cpuinfo.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_cpuinfo.h"
[10/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_egl.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_egl.h"
[11/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_endian.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_endian.h"
[12/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_error.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_error.h"
[13/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_filesystem.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_filesystem.h"
[14/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_events.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_events.h"
[15/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_gamecontroller.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_gamecontroller.h"
[16/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_gesture.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_gesture.h"
[17/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_haptic.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_haptic.h"
[18/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_guid.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_guid.h"
[19/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_hidapi.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_hidapi.h"
[20/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_hints.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_hints.h"
[21/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_keyboard.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_keyboard.h"
[22/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_keycode.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_keycode.h"
[23/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_joystick.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_joystick.h"
[24/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_loadso.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_loadso.h"
[25/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_log.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_log.h"
[26/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_locale.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_locale.h"
[27/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_main.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_main.h"
[28/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_messagebox.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_messagebox.h"
[29/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_metal.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_metal.h"
[30/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_misc.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_misc.h"
[31/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_mouse.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_mouse.h"
[32/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_mutex.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_mutex.h"
[33/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_name.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_name.h"
[34/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengl.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengl.h"
[35/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengl_glext.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengl_glext.h"
[36/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles2.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles2.h"
[37/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles.h"
[38/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles2_gl2platform.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles2_gl2platform.h"
[39/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles2_gl2ext.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles2_gl2ext.h"
[40/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles2_gl2.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles2_gl2.h"
[41/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_opengles2_khrplatform.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_opengles2_khrplatform.h"
[42/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_pixels.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_pixels.h"
[43/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_platform.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_platform.h"
[44/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_quit.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_quit.h"
[45/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_power.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_power.h"
[46/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_rect.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_rect.h"
[47/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_rwops.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_rwops.h"
[48/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_render.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_render.h"
[49/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_scancode.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_scancode.h"
[50/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_sensor.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_sensor.h"
[51/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_shape.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_shape.h"
[52/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_surface.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_surface.h"
[53/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_stdinc.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_stdinc.h"
[54/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_system.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_system.h"
[55/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_syswm.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_syswm.h"
[56/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_assert.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_assert.h"
[57/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test.h"
[58/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_common.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_common.h"
[59/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_compare.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_compare.h"
[60/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_crc32.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_crc32.h"
[61/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_font.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_font.h"
[62/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_fuzzer.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_fuzzer.h"
[63/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_harness.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_harness.h"
[64/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_log.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_log.h"
[65/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_images.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_images.h"
[66/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_md5.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_md5.h"
[67/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_thread.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_thread.h"
[68/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_memory.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_memory.h"
[69/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_test_random.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_test_random.h"
[70/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_timer.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_timer.h"
[71/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_types.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_types.h"
[72/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_version.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_version.h"
[73/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_touch.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_touch.h"
[74/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_video.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_video.h"
[75/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/close_code.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/close_code.h"
[76/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/SDL_vulkan.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/SDL_vulkan.h"
[77/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -E copy_if_different C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/src/ase-2.32.8-cdefe94ced.clean/include/begin_code.h C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/buildtrees/sdl2/x64-windows-rel/include/SDL2/begin_code.h"
[78/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_error.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_error.c
[79/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_hints.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_hints.c
[80/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_guid.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_guid.c
[81/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_list.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_list.c
[82/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_dataqueue.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_dataqueue.c
[83/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_utils.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_utils.c
[84/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe    -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /showIncludes /FoCMakeFiles\SDL2main.dir\src\main\windows\SDL_windows_main.c.obj /FdCMakeFiles\SDL2main.dir\SDL2main.pdb /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\main\windows\SDL_windows_main.c
[85/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_log.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_log.c
[86/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\atomic\SDL_atomic.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\atomic\SDL_atomic.c
[87/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL_assert.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL_assert.c
[88/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\SDL.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\SDL.c
[89/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_audiodev.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_audiodev.c
[90/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\atomic\SDL_spinlock.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\atomic\SDL_spinlock.c
[91/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_mixer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_mixer.c
[92/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_audiotypecvt.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_audiotypecvt.c
[93/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_audio.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_audio.c
[94/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_audiocvt.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_audiocvt.c
[95/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\cpuinfo\SDL_cpuinfo.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\cpuinfo\SDL_cpuinfo.c
[96/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\SDL_wave.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\SDL_wave.c
[97/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_clipboardevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_clipboardevents.c
[98/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_displayevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_displayevents.c
[99/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_keysym_to_scancode.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_keysym_to_scancode.c
[100/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_dropevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_dropevents.c
[101/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_keyboard.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_keyboard.c
[102/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_gesture.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_gesture.c
[103/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_quit.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_quit.c
[104/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_mouse.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_mouse.c
[105/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_touch.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_touch.c
[106/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_scancode_tables.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_scancode_tables.c
[107/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_events.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_events.c
[108/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\imKStoUCS.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\imKStoUCS.c
[109/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\events\SDL_windowevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\events\SDL_windowevents.c
[110/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\file\SDL_rwops.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\file\SDL_rwops.c
[111/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\haptic\SDL_haptic.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\haptic\SDL_haptic.c
[112/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\controller_type.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\controller_type.c
[113/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\SDL_gamecontroller.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\SDL_gamecontroller.c
[114/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\hidapi\SDL_hidapi.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\hidapi\SDL_hidapi.c
[115/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_atan2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_atan2.c
[116/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\dynapi\SDL_dynapi.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\dynapi\SDL_dynapi.c
[117/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\SDL_steam_virtual_gamepad.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\SDL_steam_virtual_gamepad.c
[118/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_exp.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_exp.c
[119/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\SDL_joystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\SDL_joystick.c
[120/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_fmod.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_fmod.c
[121/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_log10.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_log10.c
[122/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_rem_pio2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_rem_pio2.c
[123/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_pow.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_pow.c
[124/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\k_cos.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\k_cos.c
[125/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_log.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_log.c
[126/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\e_sqrt.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\e_sqrt.c
[127/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\k_tan.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\k_tan.c
[128/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_fabs.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_fabs.c
[129/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_atan.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_atan.c
[130/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\k_sin.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\k_sin.c
[131/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_cos.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_cos.c
[132/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_copysign.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_copysign.c
[133/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_floor.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_floor.c
[134/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_scalbn.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_scalbn.c
[135/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_sin.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_sin.c
[136/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\SDL_d3dmath.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\SDL_d3dmath.c
[137/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\misc\SDL_url.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\misc\SDL_url.c
[138/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\power\SDL_power.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\power\SDL_power.c
[139/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\locale\SDL_locale.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\locale\SDL_locale.c
[140/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\s_tan.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\s_tan.c
[141/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\libm\k_rem_pio2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\libm\k_rem_pio2.c
[142/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\SDL_yuv_sw.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\SDL_yuv_sw.c
[143/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\SDL_render.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\SDL_render.c
[144/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d\SDL_render_d3d.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d\SDL_render_d3d.c
[145/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d11\SDL_shaders_d3d11.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d11\SDL_shaders_d3d11.c
[146/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d12\SDL_shaders_d3d12.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d12\SDL_shaders_d3d12.c
[147/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d\SDL_shaders_d3d.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d\SDL_shaders_d3d.c
[148/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d12\SDL_render_d3d12.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d12\SDL_render_d3d12.c
[149/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\direct3d11\SDL_render_d3d11.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\direct3d11\SDL_render_d3d11.c
[150/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\opengl\SDL_shaders_gl.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\opengl\SDL_shaders_gl.c
[151/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\opengl\SDL_render_gl.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\opengl\SDL_render_gl.c
[152/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\psp\SDL_render_psp.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\psp\SDL_render_psp.c
[153/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\opengles2\SDL_shaders_gles2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\opengles2\SDL_shaders_gles2.c
[154/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\opengles\SDL_render_gles.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\opengles\SDL_render_gles.c
[155/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\ps2\SDL_render_ps2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\ps2\SDL_render_ps2.c
[156/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\opengles2\SDL_render_gles2.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\opengles2\SDL_render_gles2.c
[157/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_blendpoint.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_blendpoint.c
[158/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_drawpoint.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_drawpoint.c
[159/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\vitagxm\SDL_render_vita_gxm.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\vitagxm\SDL_render_vita_gxm.c
[160/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\vitagxm\SDL_render_vita_gxm_memory.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\vitagxm\SDL_render_vita_gxm_memory.c
[161/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_drawline.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_drawline.c
[162/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\vitagxm\SDL_render_vita_gxm_tools.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\vitagxm\SDL_render_vita_gxm_tools.c
[163/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_rotate.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_rotate.c
[164/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_render_sw.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_render_sw.c
[165/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_crc16.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_crc16.c
[166/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_triangle.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_triangle.c
[167/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_crc32.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_crc32.c
[168/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\sensor\SDL_sensor.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\sensor\SDL_sensor.c
[169/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP  /GL- /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_mslibc.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_mslibc.c
[170/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_getenv.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_getenv.c
[171/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_iconv.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_iconv.c
[172/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_malloc.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_malloc.c
[173/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_blendfillrect.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_blendfillrect.c
[174/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_qsort.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_qsort.c
[175/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_stdlib.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_stdlib.c
[176/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\timer\SDL_timer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\timer\SDL_timer.c
[177/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_strtokr.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_strtokr.c
[178/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\SDL_thread.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\SDL_thread.c
[179/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit.c
[180/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\stdlib\SDL_string.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\stdlib\SDL_string.c
[181/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_0.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_0.c
[182/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\render\software\SDL_blendline.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\render\software\SDL_blendline.c
[183/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_bmp.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_bmp.c
[184/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_copy.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_copy.c
[185/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_slow.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_slow.c
[186/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_RLEaccel.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_RLEaccel.c
[187/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_clipboard.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_clipboard.c
[188/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_fillrect.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_fillrect.c
[189/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_auto.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_auto.c
[190/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_pixels.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_pixels.c
[191/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_egl.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_egl.c
[192/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_1.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_1.c
[193/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_rect.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_rect.c
[194/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_shape.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_shape.c
[195/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_N.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_N.c
[196/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_vulkan_utils.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_vulkan_utils.c
[197/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_blit_A.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_blit_A.c
[198/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_surface.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_surface.c
[199/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_stretch.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_stretch.c
[200/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\yuv2rgb\yuv_rgb_lsx.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\yuv2rgb\yuv_rgb_lsx.c
[201/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_video.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_video.c
[202/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\disk\SDL_diskaudio.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\disk\SDL_diskaudio.c
[203/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\virtual\SDL_virtualjoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\virtual\SDL_virtualjoystick.c
[204/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\dummy\SDL_dummyaudio.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\dummy\SDL_dummyaudio.c
[205/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\dummy\SDL_nullframebuffer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\dummy\SDL_nullframebuffer.c
[206/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\dummy\SDL_nullevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\dummy\SDL_nullevents.c
[207/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\yuv2rgb\yuv_rgb_sse.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\yuv2rgb\yuv_rgb_sse.c
[208/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\yuv2rgb\yuv_rgb_std.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\yuv2rgb\yuv_rgb_std.c
[209/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\offscreen\SDL_offscreenframebuffer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\offscreen\SDL_offscreenframebuffer.c
[210/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\offscreen\SDL_offscreenevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\offscreen\SDL_offscreenevents.c
[211/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\dummy\SDL_nullvideo.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\dummy\SDL_nullvideo.c
[212/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\offscreen\SDL_offscreenopengles.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\offscreen\SDL_offscreenopengles.c
[213/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\offscreen\SDL_offscreenwindow.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\offscreen\SDL_offscreenwindow.c
[214/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\offscreen\SDL_offscreenvideo.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\offscreen\SDL_offscreenvideo.c
[215/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\SDL_yuv.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\SDL_yuv.c
[216/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\core\windows\SDL_hid.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\core\windows\SDL_hid.c
[217/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\core\windows\SDL_immdevice.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\core\windows\SDL_immdevice.c
[218/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\misc\windows\SDL_sysurl.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\misc\windows\SDL_sysurl.c
[219/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\core\windows\SDL_windows.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\core\windows\SDL_windows.c
[220/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\core\windows\SDL_xinput.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\core\windows\SDL_xinput.c
[221/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\winmm\SDL_winmm.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\winmm\SDL_winmm.c
[222/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\directsound\SDL_directsound.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\directsound\SDL_directsound.c
[223/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\wasapi\SDL_wasapi.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\wasapi\SDL_wasapi.c
[224/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\audio\wasapi\SDL_wasapi_win32.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\audio\wasapi\SDL_wasapi_win32.c
[225/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsclipboard.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsclipboard.c
[226/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsframebuffer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsframebuffer.c
[227/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsmessagebox.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsmessagebox.c
[228/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowskeyboard.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowskeyboard.c
[229/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsvulkan.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsvulkan.c
[230/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsopengles.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsopengles.c
[231/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsmodes.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsmodes.c
[232/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsevents.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsevents.c
[233/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsmouse.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsmouse.c
[234/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsopengl.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsopengl.c
[235/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\generic\SDL_syscond.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\generic\SDL_syscond.c
[236/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsshape.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsshape.c
[237/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowswindow.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowswindow.c
[238/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\video\windows\SDL_windowsvideo.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\windows\SDL_windowsvideo.c
[239/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\windows\SDL_systhread.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\windows\SDL_systhread.c
[240/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\windows\SDL_systls.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\windows\SDL_systls.c
[241/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\windows\SDL_syscond_cv.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\windows\SDL_syscond_cv.c
[242/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\windows\SDL_sysmutex.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\windows\SDL_sysmutex.c
[243/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\thread\windows\SDL_syssem.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\thread\windows\SDL_syssem.c
[244/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_combined.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_combined.c
[245/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\power\windows\SDL_syspower.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\power\windows\SDL_syspower.c
[246/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_gamecube.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_gamecube.c
[247/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\locale\windows\SDL_syslocale.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\locale\windows\SDL_syslocale.c
[248/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\timer\windows\SDL_systimer.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\timer\windows\SDL_systimer.c
[249/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\loadso\windows\SDL_sysloadso.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\loadso\windows\SDL_sysloadso.c
[250/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_luna.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_luna.c
[251/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\sensor\windows\SDL_windowssensor.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\sensor\windows\SDL_windowssensor.c
[252/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_stadia.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_stadia.c
[253/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_shield.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_shield.c
[254/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\filesystem\windows\SDL_sysfilesystem.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\filesystem\windows\SDL_sysfilesystem.c
[255/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_ps4.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_ps4.c
[256/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_ps3.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_ps3.c
[257/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_steamdeck.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_steamdeck.c
[258/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_ps5.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_ps5.c
[259/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_steam.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_steam.c
[260/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_rumble.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_rumble.c
[261/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_xbox360.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_xbox360.c
[262/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_xbox360w.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_xbox360w.c
[263/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_wii.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_wii.c
[264/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_switch.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_switch.c
[265/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapi_xboxone.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapi_xboxone.c
[266/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\windows\SDL_dinputjoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\windows\SDL_dinputjoystick.c
[267/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\hidapi\SDL_hidapijoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\hidapi\SDL_hidapijoystick.c
[268/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\haptic\windows\SDL_xinputhaptic.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\haptic\windows\SDL_xinputhaptic.c
[269/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\haptic\windows\SDL_windowshaptic.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\haptic\windows\SDL_windowshaptic.c
[270/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\windows\SDL_windowsjoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\windows\SDL_windowsjoystick.c
[271/278] C:/PROGRA~1/CMake/bin/cmcldeps.exe RC C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\main\windows\version.rc CMakeFiles\SDL2.dir\src\main\windows\version.rc.res.d CMakeFiles\SDL2.dir\src\main\windows\version.rc.res "Note: including file: " "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe" C:\PROGRA~2\WI3CF2~1\10\bin\100220~1.0\x64\rc.exe -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -I C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -I C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -I C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -I C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -I C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos -c65001 /DWIN32 /fo CMakeFiles\SDL2.dir\src\main\windows\version.rc.res C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\main\windows\version.rc
[272/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\windows\SDL_xinputjoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\windows\SDL_xinputjoystick.c
[273/278] C:\WINDOWS\system32\cmd.exe /C "cd . && C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\lib.exe  /machine:x64 /nologo /out:SDL2main.lib CMakeFiles\SDL2main.dir\src\main\windows\SDL_windows_main.c.obj && cd ."
[274/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\haptic\windows\SDL_dinputhaptic.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\haptic\windows\SDL_dinputhaptic.c
[275/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\windows\SDL_windows_gaming_input.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\windows\SDL_windows_gaming_input.c
[276/278] C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe   -DDLL_EXPORT -DUSING_GENERATED_CONFIG_H -D_CRT_NONSTDC_NO_DEPRECATE -D_CRT_SECURE_NO_DEPRECATE -D_CRT_SECURE_NO_WARNINGS -D_USE_MATH_DEFINES -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release\SDL2 -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel\include-config-release -IC:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\video\khronos /nologo /DWIN32 /D_WINDOWS /utf-8 /MP    -DSDL_BUILD_MAJOR_VERSION=2 -DSDL_BUILD_MINOR_VERSION=32 -DSDL_BUILD_MICRO_VERSION=8 /MD /O2 /Oi /Gy /DNDEBUG /Z7  -MD /MP /showIncludes /FoCMakeFiles\SDL2.dir\src\joystick\windows\SDL_rawinputjoystick.c.obj /FdCMakeFiles\SDL2.dir\ /FS -c C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\src\ase-2.32.8-cdefe94ced.clean\src\joystick\windows\SDL_rawinputjoystick.c
[277/278] C:\WINDOWS\system32\cmd.exe /C "cd . && "C:\Program Files\CMake\bin\cmake.exe" -E vs_link_dll --msvc-ver=1944 --intdir=CMakeFiles\SDL2.dir --rc=C:\PROGRA~2\WI3CF2~1\10\bin\100220~1.0\x64\rc.exe --mt=C:\PROGRA~2\WI3CF2~1\10\bin\100220~1.0\x64\mt.exe --manifests  -- C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\link.exe  @CMakeFiles\SDL2.rsp  /out:SDL2.dll /implib:SDL2.lib /pdb:SDL2.pdb /dll /version:2.32 -shared /machine:x64 /nologo /DEBUG /INCREMENTAL:NO /OPT:REF /OPT:ICF && cd ."
[277/278] C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Desktop\Voice-agent\vcpkg\buildtrees\sdl2\x64-windows-rel && "C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake"
-- Install configuration: "Release"
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/lib/SDL2.lib
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/bin/SDL2.dll
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/bin/SDL2.pdb
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/lib/SDL2main.lib
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2Targets.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2Targets-release.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2mainTargets.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2mainTargets-release.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2Config.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/SDL2ConfigVersion.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/cmake/sdlfind.cmake
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_assert.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_atomic.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_audio.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_bits.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_blendmode.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_clipboard.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_copying.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_cpuinfo.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_egl.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_endian.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_error.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_events.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_filesystem.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_gamecontroller.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_gesture.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_guid.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_haptic.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_hidapi.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_hints.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_joystick.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_keyboard.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_keycode.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_loadso.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_locale.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_log.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_main.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_messagebox.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_metal.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_misc.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_mouse.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_mutex.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_name.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengl.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengl_glext.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles2.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles2_gl2.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles2_gl2ext.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles2_gl2platform.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_opengles2_khrplatform.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_pixels.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_platform.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_power.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_quit.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_rect.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_render.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_rwops.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_scancode.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_sensor.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_shape.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_stdinc.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_surface.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_system.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_syswm.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_assert.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_common.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_compare.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_crc32.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_font.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_fuzzer.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_harness.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_images.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_log.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_md5.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_memory.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_test_random.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_thread.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_timer.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_touch.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_types.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_version.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_video.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_vulkan.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/begin_code.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/close_code.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_revision.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/include/SDL2/SDL_config.h
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/licenses/SDL2/LICENSE.txt
-- Installing: C:/Users/<USER>/OneDrive/Desktop/Voice-agent/vcpkg/packages/sdl2_x64-windows/lib/pkgconfig/sdl2.pc

