{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 2485130472329912235, "deps": [[5103565458935487, "futures_io", false, 15738631877131393545], [1615478164327904835, "pin_utils", false, 6444846414527782917], [1811549171721445101, "futures_channel", false, 11034357251717201261], [1906322745568073236, "pin_project_lite", false, 15321155184145598939], [5451793922601807560, "slab", false, 11781552287433461544], [7013762810557009322, "futures_sink", false, 16339093799909363168], [7620660491849607393, "futures_core", false, 10672795016643040551], [10565019901765856648, "futures_macro", false, 12880741131194560044], [15932120279885307830, "memchr", false, 14399179885751442329], [16240732885093539806, "futures_task", false, 5677929843572995544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-69dbaa518f0a463f\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}