# Voice Agent Project

## Setup Instructions

1. Clone the repository
2. Install dependencies: `cargo build`
3. Set Gemini API key:
   ```bash
   # Windows
   setx GEMINI_API_KEY "your-api-key"
   
   # Linux/Mac
   export GEMINI_API_KEY="your-api-key"
   ```
4. Run the application: `cargo run --release`

## Troubleshooting

### STT Not Working
Ensure the whisper-stream binary exists at:
`whisper.cpp/build/bin/Release/whisper-stream.exe`

If missing, build it from the whisper.cpp repository.

### LLM Responses Disabled
Set the GEMINI_API_KEY environment variable with your Gemini API key.