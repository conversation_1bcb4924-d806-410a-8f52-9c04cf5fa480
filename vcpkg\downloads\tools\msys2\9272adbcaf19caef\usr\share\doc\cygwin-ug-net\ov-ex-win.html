<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Quick Start Guide for those more experienced with Windows</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"><link rel="prev" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"><link rel="next" href="ov-ex-unix.html" title="Quick Start Guide for those more experienced with UNIX"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Quick Start Guide for those more experienced with Windows</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="overview.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Cygwin Overview</th><td width="20%" align="right">&#160;<a accesskey="n" href="ov-ex-unix.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ov-ex-win"></a>Quick Start Guide for those more experienced with Windows</h2></div></div></div><p>
If you are new to the world of UNIX, you may find it difficult to
understand at first. This guide is not meant to be comprehensive,
so we recommend that you use the many available Internet resources
to become acquainted with UNIX basics (search for "UNIX basics" or
"UNIX tutorial"). 
</p><p>
To install a basic Cygwin environment, run the Cygwin Setup program from
the <a class="ulink" href="https://cygwin.com/" target="_top">Cygwin homepage</a> and click
<code class="literal">Next</code>
at each page.  The default settings are correct for most users. If you
want to know more about what each option means, see 
<a class="xref" href="setup-net.html#internet-setup" title="Internet Setup">the section called &#8220;Internet Setup&#8221;</a>. Use the Cygwin Setup program
any time you want to update or install a Cygwin package.  If you are
installing Cygwin for a specific purpose, use it to install the tools
that you need. For example, if you want to compile C++ programs, you 
need the <code class="systemitem">gcc-g++</code> package and probably a text
editor like <code class="systemitem">nano</code>.  When running Cygwin Setup,
clicking on categories and packages in the package installation screen
will provide you with the ability to control what is installed or updated. 
</p><p>
After installation, you can find Cygwin-specific documentation in
the <code class="literal">/usr/share/doc/Cygwin/</code> directory.
</p><p>
Developers coming from a Windows background will be able to write 
console or GUI executables that rely on the Microsoft Win32 API instead
of Cygwin using the mingw32 or mingw64 cross-compiler toolchains.  The
<span class="command"><strong>-shared</strong></span> option to GCC allows to write Windows Dynamically
Linked Libraries (DLLs).  The resource compiler <span class="command"><strong>windres</strong></span>
is also provided.
</p></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="overview.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="overview.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="ov-ex-unix.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Chapter&#160;1.&#160;Cygwin Overview&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Quick Start Guide for those more experienced with UNIX</td></tr></table></div></body></html>
