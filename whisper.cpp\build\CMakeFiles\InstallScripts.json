{"InstallScripts": ["C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/ggml/src/ggml-cpu/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/src/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/tests/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/cli/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/bench/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/server/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/quantize/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/vad-speech-segments/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/stream/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/command/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/talk-llama/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/lsp/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/deprecation-warning/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/libwchess/cmake_install.cmake", "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/examples/wchess/wchess.cmd/cmake_install.cmake"], "Parallel": false}