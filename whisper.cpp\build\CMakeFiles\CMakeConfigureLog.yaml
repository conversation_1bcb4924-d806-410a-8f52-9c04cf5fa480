
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.14+a129329f1 for .NET Framework
      Build started 17-07-2025 00:34:20.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.85
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/4.1.0-rc2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link"
      - "C:/nvm4w/nodejs/lld-link.com"
      - "C:/nvm4w/nodejs/lld-link.exe"
      - "C:/nvm4w/nodejs/lld-link"
      - "C:/Program Files/Go/bin/lld-link.com"
      - "C:/Program Files/Go/bin/lld-link.exe"
      - "C:/Program Files/Go/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Void/bin/lld-link.com"
      - "C:/Program Files/Void/bin/lld-link.exe"
      - "C:/Program Files/Void/bin/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/go/bin/lld-link.com"
      - "C:/Users/<USER>/go/bin/lld-link.exe"
      - "C:/Users/<USER>/go/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Users/<USER>/AppData/Local/nvm/mt.com"
      - "C:/Users/<USER>/AppData/Local/nvm/mt.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/mt"
      - "C:/nvm4w/nodejs/mt.com"
      - "C:/nvm4w/nodejs/mt.exe"
      - "C:/nvm4w/nodejs/mt"
      - "C:/Program Files/Go/bin/mt.com"
      - "C:/Program Files/Go/bin/mt.exe"
      - "C:/Program Files/Go/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/Void/bin/mt.com"
      - "C:/Program Files/Void/bin/mt.exe"
      - "C:/Program Files/Void/bin/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Users/<USER>/.cargo/bin/mt.com"
      - "C:/Users/<USER>/.cargo/bin/mt.exe"
      - "C:/Users/<USER>/.cargo/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "C:/Users/<USER>/go/bin/mt.com"
      - "C:/Users/<USER>/go/bin/mt.exe"
      - "C:/Users/<USER>/go/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.14+a129329f1 for .NET Framework
      Build started 17-07-2025 00:34:22.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\4.1.0-rc2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.76
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/4.1.0-rc2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/lld-link"
      - "C:/nvm4w/nodejs/lld-link.com"
      - "C:/nvm4w/nodejs/lld-link.exe"
      - "C:/nvm4w/nodejs/lld-link"
      - "C:/Program Files/Go/bin/lld-link.com"
      - "C:/Program Files/Go/bin/lld-link.exe"
      - "C:/Program Files/Go/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Void/bin/lld-link.com"
      - "C:/Program Files/Void/bin/lld-link.exe"
      - "C:/Program Files/Void/bin/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "C:/Users/<USER>/go/bin/lld-link.com"
      - "C:/Users/<USER>/go/bin/lld-link.exe"
      - "C:/Users/<USER>/go/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Users/<USER>/AppData/Local/nvm/rc.com"
      - "C:/Users/<USER>/AppData/Local/nvm/rc.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/rc"
      - "C:/nvm4w/nodejs/rc.com"
      - "C:/nvm4w/nodejs/rc.exe"
      - "C:/nvm4w/nodejs/rc"
      - "C:/Program Files/Go/bin/rc.com"
      - "C:/Program Files/Go/bin/rc.exe"
      - "C:/Program Files/Go/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/Void/bin/rc.com"
      - "C:/Program Files/Void/bin/rc.exe"
      - "C:/Program Files/Void/bin/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Users/<USER>/.cargo/bin/rc.com"
      - "C:/Users/<USER>/.cargo/bin/rc.exe"
      - "C:/Users/<USER>/.cargo/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "C:/Users/<USER>/go/bin/rc.com"
      - "C:/Users/<USER>/go/bin/rc.exe"
      - "C:/Users/<USER>/go/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/rc"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/rc"
      - "C:/Program Files (x86)/whisper.cpp/rc.com"
      - "C:/Program Files (x86)/whisper.cpp/rc.exe"
      - "C:/Program Files (x86)/whisper.cpp/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-dlw1gj"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-dlw1gj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-dlw1gj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_69ed7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:23.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dlw1gj\\cmTC_69ed7.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_69ed7.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dlw1gj\\Debug\\".
          Creating directory "cmTC_69ed7.dir\\Debug\\cmTC_69ed7.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_69ed7.dir\\Debug\\cmTC_69ed7.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_69ed7.dir\\Debug\\cmTC_69ed7.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_69ed7.dir\\Debug\\\\" /Fd"cmTC_69ed7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_69ed7.dir\\Debug\\\\" /Fd"cmTC_69ed7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dlw1gj\\Debug\\cmTC_69ed7.exe" /INCREMENTAL /ILK:"cmTC_69ed7.dir\\Debug\\cmTC_69ed7.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-dlw1gj/Debug/cmTC_69ed7.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-dlw1gj/Debug/cmTC_69ed7.lib" /MACHINE:X64  /machine:x64 cmTC_69ed7.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_69ed7.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dlw1gj\\Debug\\cmTC_69ed7.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_69ed7.dir\\Debug\\cmTC_69ed7.tlog\\unsuccessfulbuild".
          Touching "cmTC_69ed7.dir\\Debug\\cmTC_69ed7.tlog\\cmTC_69ed7.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dlw1gj\\cmTC_69ed7.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.91
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s9u35"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s9u35"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s9u35'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6b566.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:24.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s9u35\\cmTC_6b566.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6b566.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s9u35\\Debug\\".
          Creating directory "cmTC_6b566.dir\\Debug\\cmTC_6b566.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6b566.dir\\Debug\\cmTC_6b566.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6b566.dir\\Debug\\cmTC_6b566.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_6b566.dir\\Debug\\\\" /Fd"cmTC_6b566.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_6b566.dir\\Debug\\\\" /Fd"cmTC_6b566.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s9u35\\Debug\\cmTC_6b566.exe" /INCREMENTAL /ILK:"cmTC_6b566.dir\\Debug\\cmTC_6b566.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s9u35/Debug/cmTC_6b566.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s9u35/Debug/cmTC_6b566.lib" /MACHINE:X64  /machine:x64 cmTC_6b566.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_6b566.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s9u35\\Debug\\cmTC_6b566.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_6b566.dir\\Debug\\cmTC_6b566.tlog\\unsuccessfulbuild".
          Touching "cmTC_6b566.dir\\Debug\\cmTC_6b566.tlog\\cmTC_6b566.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s9u35\\cmTC_6b566.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Preview/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindGit.cmake:86 (find_program)"
      - "cmake/git-vars.cmake:1 (find_package)"
      - "CMakeLists.txt:26 (include)"
    mode: "program"
    variable: "GIT_EXECUTABLE"
    description: "Git command line client"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git.cmd"
      - "git"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/"
    searched_directories:
      - "C:/Windows/System32/git.cmd.com"
      - "C:/Windows/System32/git.cmd.exe"
      - "C:/Windows/System32/git.cmd"
      - "C:/Windows/git.cmd.com"
      - "C:/Windows/git.cmd.exe"
      - "C:/Windows/git.cmd"
      - "C:/Windows/System32/wbem/git.cmd.com"
      - "C:/Windows/System32/wbem/git.cmd.exe"
      - "C:/Windows/System32/wbem/git.cmd"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.cmd"
      - "C:/Windows/System32/OpenSSH/git.cmd.com"
      - "C:/Windows/System32/OpenSSH/git.cmd.exe"
      - "C:/Windows/System32/OpenSSH/git.cmd"
      - "C:/Users/<USER>/AppData/Local/nvm/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/nvm/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/git.cmd"
      - "C:/nvm4w/nodejs/git.cmd.com"
      - "C:/nvm4w/nodejs/git.cmd.exe"
      - "C:/nvm4w/nodejs/git.cmd"
      - "C:/Program Files/Go/bin/git.cmd.com"
      - "C:/Program Files/Go/bin/git.cmd.exe"
      - "C:/Program Files/Go/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.cmd"
      - "C:/Program Files/Git/cmd/git.cmd.com"
      - "C:/Program Files/Git/cmd/git.cmd.exe"
      - "C:/Program Files/Git/cmd/git.cmd"
      - "C:/Program Files/Void/bin/git.cmd.com"
      - "C:/Program Files/Void/bin/git.cmd.exe"
      - "C:/Program Files/Void/bin/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/Users/<USER>/.cargo/bin/git.cmd.com"
      - "C:/Users/<USER>/.cargo/bin/git.cmd.exe"
      - "C:/Users/<USER>/.cargo/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/git.cmd"
      - "C:/Users/<USER>/go/bin/git.cmd.com"
      - "C:/Users/<USER>/go/bin/git.cmd.exe"
      - "C:/Users/<USER>/go/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/git.cmd"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/git.cmd.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/git.cmd"
      - "C:/Program Files/bin/git.cmd.com"
      - "C:/Program Files/bin/git.cmd.exe"
      - "C:/Program Files/bin/git.cmd"
      - "C:/Program Files/sbin/git.cmd.com"
      - "C:/Program Files/sbin/git.cmd.exe"
      - "C:/Program Files/sbin/git.cmd"
      - "C:/Program Files/git.cmd.com"
      - "C:/Program Files/git.cmd.exe"
      - "C:/Program Files/git.cmd"
      - "C:/Program Files (x86)/bin/git.cmd.com"
      - "C:/Program Files (x86)/bin/git.cmd.exe"
      - "C:/Program Files (x86)/bin/git.cmd"
      - "C:/Program Files (x86)/sbin/git.cmd.com"
      - "C:/Program Files (x86)/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/sbin/git.cmd"
      - "C:/Program Files (x86)/git.cmd.com"
      - "C:/Program Files (x86)/git.cmd.exe"
      - "C:/Program Files (x86)/git.cmd"
      - "C:/Program Files/CMake/bin/git.cmd.com"
      - "C:/Program Files/CMake/bin/git.cmd.exe"
      - "C:/Program Files/CMake/bin/git.cmd"
      - "C:/Program Files/CMake/sbin/git.cmd.com"
      - "C:/Program Files/CMake/sbin/git.cmd.exe"
      - "C:/Program Files/CMake/sbin/git.cmd"
      - "C:/Program Files/CMake/git.cmd.com"
      - "C:/Program Files/CMake/git.cmd.exe"
      - "C:/Program Files/CMake/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/git.cmd"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd.com"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd.exe"
      - "C:/Program Files (x86)/whisper.cpp/git.cmd"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.com"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd.exe"
      - "C:/Users/<USER>/AppData/Local/Atlassian/SourceTree/git_local/bin/git.cmd"
      - "C:/Windows/System32/git.com"
      - "C:/Windows/System32/git.exe"
      - "C:/Windows/System32/git"
      - "C:/Windows/git.com"
      - "C:/Windows/git.exe"
      - "C:/Windows/git"
      - "C:/Windows/System32/wbem/git.com"
      - "C:/Windows/System32/wbem/git.exe"
      - "C:/Windows/System32/wbem/git"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git"
      - "C:/Windows/System32/OpenSSH/git.com"
      - "C:/Windows/System32/OpenSSH/git.exe"
      - "C:/Windows/System32/OpenSSH/git"
      - "C:/Users/<USER>/AppData/Local/nvm/git.com"
      - "C:/Users/<USER>/AppData/Local/nvm/git.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/git"
      - "C:/nvm4w/nodejs/git.com"
      - "C:/nvm4w/nodejs/git.exe"
      - "C:/nvm4w/nodejs/git"
      - "C:/Program Files/Go/bin/git.com"
      - "C:/Program Files/Go/bin/git.exe"
      - "C:/Program Files/Go/bin/git"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git"
      - "C:/Program Files/Git/cmd/git.com"
    found: "C:/Program Files/Git/cmd/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceCompiles.cmake:103 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "ggml/CMakeLists.txt:229 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-8kho43"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-8kho43"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-8kho43'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_32503.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:25.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\cmTC_32503.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_32503.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\Debug\\".
          Creating directory "cmTC_32503.dir\\Debug\\cmTC_32503.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_32503.dir\\Debug\\cmTC_32503.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_32503.dir\\Debug\\cmTC_32503.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_32503.dir\\Debug\\\\" /Fd"cmTC_32503.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_32503.dir\\Debug\\\\" /Fd"cmTC_32503.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\src.c"
          src.c
        C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\src.c(1,1): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\cmTC_32503.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\cmTC_32503.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\cmTC_32503.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\src.c(1,1): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8kho43\\cmTC_32503.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.45
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "ggml/CMakeLists.txt:229 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pfpxn1"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pfpxn1"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pfpxn1'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f1980.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:26.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\cmTC_f1980.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_f1980.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\Debug\\".
          Creating directory "cmTC_f1980.dir\\Debug\\cmTC_f1980.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_f1980.dir\\Debug\\cmTC_f1980.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_f1980.dir\\Debug\\cmTC_f1980.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_f1980.dir\\Debug\\\\" /Fd"cmTC_f1980.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_f1980.dir\\Debug\\\\" /Fd"cmTC_f1980.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\Debug\\cmTC_f1980.exe" /INCREMENTAL /ILK:"cmTC_f1980.dir\\Debug\\cmTC_f1980.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pfpxn1/Debug/cmTC_f1980.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-pfpxn1/Debug/cmTC_f1980.lib" /MACHINE:X64  /machine:x64 cmTC_f1980.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\cmTC_f1980.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\cmTC_f1980.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\cmTC_f1980.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pfpxn1\\cmTC_f1980.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.49
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckLibraryExists.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "ggml/CMakeLists.txt:229 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-g6cduh"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-g6cduh"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-g6cduh'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_58a3e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:27.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\cmTC_58a3e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_58a3e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\Debug\\".
          Creating directory "cmTC_58a3e.dir\\Debug\\cmTC_58a3e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_58a3e.dir\\Debug\\cmTC_58a3e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_58a3e.dir\\Debug\\cmTC_58a3e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_58a3e.dir\\Debug\\\\" /Fd"cmTC_58a3e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_58a3e.dir\\Debug\\\\" /Fd"cmTC_58a3e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\Debug\\cmTC_58a3e.exe" /INCREMENTAL /ILK:"cmTC_58a3e.dir\\Debug\\cmTC_58a3e.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-g6cduh/Debug/cmTC_58a3e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-g6cduh/Debug/cmTC_58a3e.lib" /MACHINE:X64  /machine:x64 cmTC_58a3e.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\cmTC_58a3e.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\cmTC_58a3e.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\cmTC_58a3e.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g6cduh\\cmTC_58a3e.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.46
        
      exitCode: 1
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:69 (find_program)"
    mode: "program"
    variable: "GGML_CCACHE_FOUND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ccache"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/ccache.com"
      - "C:/Windows/System32/ccache.exe"
      - "C:/Windows/System32/ccache"
      - "C:/Windows/ccache.com"
      - "C:/Windows/ccache.exe"
      - "C:/Windows/ccache"
      - "C:/Windows/System32/wbem/ccache.com"
      - "C:/Windows/System32/wbem/ccache.exe"
      - "C:/Windows/System32/wbem/ccache"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ccache"
      - "C:/Windows/System32/OpenSSH/ccache.com"
      - "C:/Windows/System32/OpenSSH/ccache.exe"
      - "C:/Windows/System32/OpenSSH/ccache"
      - "C:/Users/<USER>/AppData/Local/nvm/ccache.com"
      - "C:/Users/<USER>/AppData/Local/nvm/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/ccache"
      - "C:/nvm4w/nodejs/ccache.com"
      - "C:/nvm4w/nodejs/ccache.exe"
      - "C:/nvm4w/nodejs/ccache"
      - "C:/Program Files/Go/bin/ccache.com"
      - "C:/Program Files/Go/bin/ccache.exe"
      - "C:/Program Files/Go/bin/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ccache"
      - "C:/Program Files/Git/cmd/ccache.com"
      - "C:/Program Files/Git/cmd/ccache.exe"
      - "C:/Program Files/Git/cmd/ccache"
      - "C:/Program Files/Void/bin/ccache.com"
      - "C:/Program Files/Void/bin/ccache.exe"
      - "C:/Program Files/Void/bin/ccache"
      - "C:/Program Files/CMake/bin/ccache.com"
      - "C:/Program Files/CMake/bin/ccache.exe"
      - "C:/Program Files/CMake/bin/ccache"
      - "C:/Users/<USER>/.cargo/bin/ccache.com"
      - "C:/Users/<USER>/.cargo/bin/ccache.exe"
      - "C:/Users/<USER>/.cargo/bin/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ccache"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ccache"
      - "C:/Users/<USER>/go/bin/ccache.com"
      - "C:/Users/<USER>/go/bin/ccache.exe"
      - "C:/Users/<USER>/go/bin/ccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/ccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/ccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/ccache"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/ccache.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/ccache.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/ccache"
      - "C:/Program Files/bin/ccache.com"
      - "C:/Program Files/bin/ccache.exe"
      - "C:/Program Files/bin/ccache"
      - "C:/Program Files/sbin/ccache.com"
      - "C:/Program Files/sbin/ccache.exe"
      - "C:/Program Files/sbin/ccache"
      - "C:/Program Files/ccache.com"
      - "C:/Program Files/ccache.exe"
      - "C:/Program Files/ccache"
      - "C:/Program Files (x86)/bin/ccache.com"
      - "C:/Program Files (x86)/bin/ccache.exe"
      - "C:/Program Files (x86)/bin/ccache"
      - "C:/Program Files (x86)/sbin/ccache.com"
      - "C:/Program Files (x86)/sbin/ccache.exe"
      - "C:/Program Files (x86)/sbin/ccache"
      - "C:/Program Files (x86)/ccache.com"
      - "C:/Program Files (x86)/ccache.exe"
      - "C:/Program Files (x86)/ccache"
      - "C:/Program Files/CMake/bin/ccache.com"
      - "C:/Program Files/CMake/bin/ccache.exe"
      - "C:/Program Files/CMake/bin/ccache"
      - "C:/Program Files/CMake/sbin/ccache.com"
      - "C:/Program Files/CMake/sbin/ccache.exe"
      - "C:/Program Files/CMake/sbin/ccache"
      - "C:/Program Files/CMake/ccache.com"
      - "C:/Program Files/CMake/ccache.exe"
      - "C:/Program Files/CMake/ccache"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/ccache"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/ccache"
      - "C:/Program Files (x86)/whisper.cpp/ccache.com"
      - "C:/Program Files (x86)/whisper.cpp/ccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/ccache"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:70 (find_program)"
    mode: "program"
    variable: "GGML_SCCACHE_FOUND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "sccache"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/sccache.com"
      - "C:/Windows/System32/sccache.exe"
      - "C:/Windows/System32/sccache"
      - "C:/Windows/sccache.com"
      - "C:/Windows/sccache.exe"
      - "C:/Windows/sccache"
      - "C:/Windows/System32/wbem/sccache.com"
      - "C:/Windows/System32/wbem/sccache.exe"
      - "C:/Windows/System32/wbem/sccache"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/sccache"
      - "C:/Windows/System32/OpenSSH/sccache.com"
      - "C:/Windows/System32/OpenSSH/sccache.exe"
      - "C:/Windows/System32/OpenSSH/sccache"
      - "C:/Users/<USER>/AppData/Local/nvm/sccache.com"
      - "C:/Users/<USER>/AppData/Local/nvm/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/sccache"
      - "C:/nvm4w/nodejs/sccache.com"
      - "C:/nvm4w/nodejs/sccache.exe"
      - "C:/nvm4w/nodejs/sccache"
      - "C:/Program Files/Go/bin/sccache.com"
      - "C:/Program Files/Go/bin/sccache.exe"
      - "C:/Program Files/Go/bin/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/sccache"
      - "C:/Program Files/Git/cmd/sccache.com"
      - "C:/Program Files/Git/cmd/sccache.exe"
      - "C:/Program Files/Git/cmd/sccache"
      - "C:/Program Files/Void/bin/sccache.com"
      - "C:/Program Files/Void/bin/sccache.exe"
      - "C:/Program Files/Void/bin/sccache"
      - "C:/Program Files/CMake/bin/sccache.com"
      - "C:/Program Files/CMake/bin/sccache.exe"
      - "C:/Program Files/CMake/bin/sccache"
      - "C:/Users/<USER>/.cargo/bin/sccache.com"
      - "C:/Users/<USER>/.cargo/bin/sccache.exe"
      - "C:/Users/<USER>/.cargo/bin/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sccache"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/sccache"
      - "C:/Users/<USER>/go/bin/sccache.com"
      - "C:/Users/<USER>/go/bin/sccache.exe"
      - "C:/Users/<USER>/go/bin/sccache"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/sccache.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/sccache.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/sccache"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/sccache.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/sccache.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/sccache"
      - "C:/Program Files/bin/sccache.com"
      - "C:/Program Files/bin/sccache.exe"
      - "C:/Program Files/bin/sccache"
      - "C:/Program Files/sbin/sccache.com"
      - "C:/Program Files/sbin/sccache.exe"
      - "C:/Program Files/sbin/sccache"
      - "C:/Program Files/sccache.com"
      - "C:/Program Files/sccache.exe"
      - "C:/Program Files/sccache"
      - "C:/Program Files (x86)/bin/sccache.com"
      - "C:/Program Files (x86)/bin/sccache.exe"
      - "C:/Program Files (x86)/bin/sccache"
      - "C:/Program Files (x86)/sbin/sccache.com"
      - "C:/Program Files (x86)/sbin/sccache.exe"
      - "C:/Program Files (x86)/sbin/sccache"
      - "C:/Program Files (x86)/sccache.com"
      - "C:/Program Files (x86)/sccache.exe"
      - "C:/Program Files (x86)/sccache"
      - "C:/Program Files/CMake/bin/sccache.com"
      - "C:/Program Files/CMake/bin/sccache.exe"
      - "C:/Program Files/CMake/bin/sccache"
      - "C:/Program Files/CMake/sbin/sccache.com"
      - "C:/Program Files/CMake/sbin/sccache.exe"
      - "C:/Program Files/CMake/sbin/sccache"
      - "C:/Program Files/CMake/sccache.com"
      - "C:/Program Files/CMake/sccache.exe"
      - "C:/Program Files/CMake/sccache"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/sccache"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/sccache"
      - "C:/Program Files (x86)/whisper.cpp/sccache.com"
      - "C:/Program Files (x86)/whisper.cpp/sccache.exe"
      - "C:/Program Files (x86)/whisper.cpp/sccache"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:251 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:558 (_OPENMP_GET_FLAGS)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-996ce8"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-996ce8"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-996ce8'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_65354.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:27.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\cmTC_65354.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_65354.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\Debug\\".
          Creating directory "cmTC_65354.dir\\Debug\\cmTC_65354.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_65354.dir\\Debug\\cmTC_65354.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_65354.dir\\Debug\\cmTC_65354.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_65354.dir\\Debug\\\\" /Fd"cmTC_65354.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_65354.dir\\Debug\\\\" /Fd"cmTC_65354.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\OpenMPTryFlag.c"
          OpenMPTryFlag.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\Debug\\cmTC_65354.exe" /INCREMENTAL /ILK:"cmTC_65354.dir\\Debug\\cmTC_65354.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-996ce8/Debug/cmTC_65354.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-996ce8/Debug/cmTC_65354.lib" /MACHINE:X64  /machine:x64 cmTC_65354.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_65354.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\Debug\\cmTC_65354.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_65354.dir\\Debug\\cmTC_65354.tlog\\unsuccessfulbuild".
          Touching "cmTC_65354.dir\\Debug\\cmTC_65354.tlog\\cmTC_65354.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-996ce8\\cmTC_65354.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:251 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:558 (_OPENMP_GET_FLAGS)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-4w4uup"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-4w4uup"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-4w4uup'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_43afd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:28.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\cmTC_43afd.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_43afd.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\Debug\\".
          Creating directory "cmTC_43afd.dir\\Debug\\cmTC_43afd.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_43afd.dir\\Debug\\cmTC_43afd.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_43afd.dir\\Debug\\cmTC_43afd.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_43afd.dir\\Debug\\\\" /Fd"cmTC_43afd.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_43afd.dir\\Debug\\\\" /Fd"cmTC_43afd.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\OpenMPTryFlag.cpp"
          OpenMPTryFlag.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\Debug\\cmTC_43afd.exe" /INCREMENTAL /ILK:"cmTC_43afd.dir\\Debug\\cmTC_43afd.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-4w4uup/Debug/cmTC_43afd.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-4w4uup/Debug/cmTC_43afd.lib" /MACHINE:X64  /machine:x64 cmTC_43afd.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_43afd.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\Debug\\cmTC_43afd.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_43afd.dir\\Debug\\cmTC_43afd.tlog\\unsuccessfulbuild".
          Touching "cmTC_43afd.dir\\Debug\\cmTC_43afd.tlog\\cmTC_43afd.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4w4uup\\cmTC_43afd.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:492 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:632 (_OPENMP_GET_SPEC_DATE)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-2lgn2x"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-2lgn2x"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-2lgn2x'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_4e795.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:29.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\cmTC_4e795.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_4e795.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\Debug\\".
          Creating directory "cmTC_4e795.dir\\Debug\\cmTC_4e795.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_4e795.dir\\Debug\\cmTC_4e795.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_4e795.dir\\Debug\\cmTC_4e795.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_4e795.dir\\Debug\\\\" /Fd"cmTC_4e795.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /openmp /std:c11 /Fo"cmTC_4e795.dir\\Debug\\\\" /Fd"cmTC_4e795.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\OpenMPCheckVersion.c"
          OpenMPCheckVersion.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\Debug\\cmTC_4e795.exe" /INCREMENTAL /ILK:"cmTC_4e795.dir\\Debug\\cmTC_4e795.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-2lgn2x/Debug/cmTC_4e795.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-2lgn2x/Debug/cmTC_4e795.lib" /MACHINE:X64  /machine:x64 cmTC_4e795.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_4e795.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\Debug\\cmTC_4e795.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_4e795.dir\\Debug\\cmTC_4e795.tlog\\unsuccessfulbuild".
          Touching "cmTC_4e795.dir\\Debug\\cmTC_4e795.tlog\\cmTC_4e795.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2lgn2x\\cmTC_4e795.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:492 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/FindOpenMP.cmake:632 (_OPENMP_GET_SPEC_DATE)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:71 (find_package)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-il5rb8"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-il5rb8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-il5rb8'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_19695.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:30.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\cmTC_19695.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_19695.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\Debug\\".
          Creating directory "cmTC_19695.dir\\Debug\\cmTC_19695.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_19695.dir\\Debug\\cmTC_19695.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_19695.dir\\Debug\\cmTC_19695.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_19695.dir\\Debug\\\\" /Fd"cmTC_19695.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /openmp /std:c++17 /Fo"cmTC_19695.dir\\Debug\\\\" /Fd"cmTC_19695.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\OpenMPCheckVersion.cpp"
          OpenMPCheckVersion.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\Debug\\cmTC_19695.exe" /INCREMENTAL /ILK:"cmTC_19695.dir\\Debug\\cmTC_19695.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-il5rb8/Debug/cmTC_19695.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-il5rb8/Debug/cmTC_19695.lib" /MACHINE:X64  /machine:x64 cmTC_19695.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_19695.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\Debug\\cmTC_19695.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_19695.dir\\Debug\\cmTC_19695.tlog\\unsuccessfulbuild".
          Touching "cmTC_19695.dir\\Debug\\cmTC_19695.tlog\\cmTC_19695.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-il5rb8\\cmTC_19695.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:80 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX_1"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s43ms"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s43ms"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s43ms'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_8e664.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:30.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\cmTC_8e664.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_8e664.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\Debug\\".
          Creating directory "cmTC_8e664.dir\\Debug\\cmTC_8e664.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_8e664.dir\\Debug\\cmTC_8e664.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_8e664.dir\\Debug\\cmTC_8e664.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_8e664.dir\\Debug\\\\" /Fd"cmTC_8e664.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_8e664.dir\\Debug\\\\" /Fd"cmTC_8e664.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\Debug\\cmTC_8e664.exe" /INCREMENTAL /ILK:"cmTC_8e664.dir\\Debug\\cmTC_8e664.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s43ms/Debug/cmTC_8e664.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-7s43ms/Debug/cmTC_8e664.lib" /MACHINE:X64  /machine:x64 cmTC_8e664.dir\\Debug\\src.obj
          cmTC_8e664.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\Debug\\cmTC_8e664.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_8e664.dir\\Debug\\cmTC_8e664.tlog\\unsuccessfulbuild".
          Touching "cmTC_8e664.dir\\Debug\\cmTC_8e664.tlog\\cmTC_8e664.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7s43ms\\cmTC_8e664.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.63
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:87 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX2_1"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cvh2u1"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cvh2u1"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX2_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cvh2u1'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_fa0cc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:31.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\cmTC_fa0cc.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_fa0cc.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\Debug\\".
          Creating directory "cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_fa0cc.dir\\Debug\\\\" /Fd"cmTC_fa0cc.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX2_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_fa0cc.dir\\Debug\\\\" /Fd"cmTC_fa0cc.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\Debug\\cmTC_fa0cc.exe" /INCREMENTAL /ILK:"cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cvh2u1/Debug/cmTC_fa0cc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-cvh2u1/Debug/cmTC_fa0cc.lib" /MACHINE:X64  /machine:x64 cmTC_fa0cc.dir\\Debug\\src.obj
          cmTC_fa0cc.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\Debug\\cmTC_fa0cc.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.tlog\\unsuccessfulbuild".
          Touching "cmTC_fa0cc.dir\\Debug\\cmTC_fa0cc.tlog\\cmTC_fa0cc.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cvh2u1\\cmTC_fa0cc.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.60
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX2_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:88 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_FMA_1"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-yiu85s"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-yiu85s"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_FMA_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-yiu85s'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6211b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:32.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\cmTC_6211b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6211b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\Debug\\".
          Creating directory "cmTC_6211b.dir\\Debug\\cmTC_6211b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6211b.dir\\Debug\\cmTC_6211b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6211b.dir\\Debug\\cmTC_6211b.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_6211b.dir\\Debug\\\\" /Fd"cmTC_6211b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_FMA_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_6211b.dir\\Debug\\\\" /Fd"cmTC_6211b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\Debug\\cmTC_6211b.exe" /INCREMENTAL /ILK:"cmTC_6211b.dir\\Debug\\cmTC_6211b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-yiu85s/Debug/cmTC_6211b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-yiu85s/Debug/cmTC_6211b.lib" /MACHINE:X64  /machine:x64 cmTC_6211b.dir\\Debug\\src.obj
          cmTC_6211b.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\Debug\\cmTC_6211b.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_6211b.dir\\Debug\\cmTC_6211b.tlog\\unsuccessfulbuild".
          Touching "cmTC_6211b.dir\\Debug\\cmTC_6211b.tlog\\cmTC_6211b.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yiu85s\\cmTC_6211b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
    runResult:
      variable: "HAS_FMA_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_1"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-48wz12"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-48wz12"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_1_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-48wz12'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c77a3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:33.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\cmTC_c77a3.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c77a3.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\Debug\\".
          Creating directory "cmTC_c77a3.dir\\Debug\\cmTC_c77a3.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c77a3.dir\\Debug\\cmTC_c77a3.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c77a3.dir\\Debug\\cmTC_c77a3.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_c77a3.dir\\Debug\\\\" /Fd"cmTC_c77a3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_1 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /std:c11 /Fo"cmTC_c77a3.dir\\Debug\\\\" /Fd"cmTC_c77a3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\Debug\\cmTC_c77a3.exe" /INCREMENTAL /ILK:"cmTC_c77a3.dir\\Debug\\cmTC_c77a3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-48wz12/Debug/cmTC_c77a3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-48wz12/Debug/cmTC_c77a3.lib" /MACHINE:X64  /machine:x64 cmTC_c77a3.dir\\Debug\\src.obj
          cmTC_c77a3.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\Debug\\cmTC_c77a3.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c77a3.dir\\Debug\\cmTC_c77a3.tlog\\unsuccessfulbuild".
          Touching "cmTC_c77a3.dir\\Debug\\cmTC_c77a3.tlog\\cmTC_c77a3.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-48wz12\\cmTC_c77a3.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.65
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_1_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CheckCSourceRuns.cmake:88 (cmake_check_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:61 (check_c_source_runs)"
      - "ggml/src/ggml-cpu/cmake/FindSIMD.cmake:95 (check_sse)"
      - "ggml/src/ggml-cpu/CMakeLists.txt:240 (include)"
      - "ggml/src/CMakeLists.txt:361 (ggml_add_cpu_backend_variant_impl)"
    checks:
      - "Performing Test HAS_AVX512_2"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-d3cqjy"
      binary: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-d3cqjy"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
    buildResult:
      variable: "HAS_AVX512_2_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-d3cqjy'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Preview/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9f044.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.14+a129329f1 for .NET Framework
        Build started 17-07-2025 00:34:34.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\cmTC_9f044.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_9f044.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\Debug\\".
          Creating directory "cmTC_9f044.dir\\Debug\\cmTC_9f044.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_9f044.dir\\Debug\\cmTC_9f044.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_9f044.dir\\Debug\\cmTC_9f044.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /std:c11 /Fo"cmTC_9f044.dir\\Debug\\\\" /Fd"cmTC_9f044.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35213 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAS_AVX512_2 /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /arch:AVX512 /fp:precise /std:c11 /Fo"cmTC_9f044.dir\\Debug\\\\" /Fd"cmTC_9f044.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\Debug\\cmTC_9f044.exe" /INCREMENTAL /ILK:"cmTC_9f044.dir\\Debug\\cmTC_9f044.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-d3cqjy/Debug/cmTC_9f044.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/CMakeScratch/TryCompile-d3cqjy/Debug/cmTC_9f044.lib" /MACHINE:X64  /machine:x64 cmTC_9f044.dir\\Debug\\src.obj
          cmTC_9f044.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\Debug\\cmTC_9f044.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_9f044.dir\\Debug\\cmTC_9f044.tlog\\unsuccessfulbuild".
          Touching "cmTC_9f044.dir\\Debug\\cmTC_9f044.tlog\\cmTC_9f044.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\Voice-agent\\whisper.cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d3cqjy\\cmTC_9f044.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 0
    runResult:
      variable: "HAS_AVX512_2_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Illegal instruction
      exitCode: "FAILED_TO_RUN"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/src/CMakeLists.txt:382 (find_library)"
    mode: "library"
    variable: "MATH_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "m"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/lib/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "/bin/"
    searched_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/lib/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/lib/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "/bin/"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/whisper.cpp/bin"
        - "C:/Program Files/CMake/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "ggml/CMakeLists.txt:298 (find_program)"
    mode: "program"
    variable: "GIT_EXE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
      - "git.exe"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/git.com"
      - "C:/Windows/System32/git.exe"
      - "C:/Windows/System32/git"
      - "C:/Windows/git.com"
      - "C:/Windows/git.exe"
      - "C:/Windows/git"
      - "C:/Windows/System32/wbem/git.com"
      - "C:/Windows/System32/wbem/git.exe"
      - "C:/Windows/System32/wbem/git"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git"
      - "C:/Windows/System32/OpenSSH/git.com"
      - "C:/Windows/System32/OpenSSH/git.exe"
      - "C:/Windows/System32/OpenSSH/git"
      - "C:/Users/<USER>/AppData/Local/nvm/git.com"
      - "C:/Users/<USER>/AppData/Local/nvm/git.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/git"
      - "C:/nvm4w/nodejs/git.com"
      - "C:/nvm4w/nodejs/git.exe"
      - "C:/nvm4w/nodejs/git"
      - "C:/Program Files/Go/bin/git.com"
      - "C:/Program Files/Go/bin/git.exe"
      - "C:/Program Files/Go/bin/git"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git"
      - "C:/Program Files/Git/cmd/git.com"
    found: "C:/Program Files/Git/cmd/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:171 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "GITCOMMAND"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "git"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/git.com"
      - "C:/Windows/System32/git.exe"
      - "C:/Windows/System32/git"
      - "C:/Windows/git.com"
      - "C:/Windows/git.exe"
      - "C:/Windows/git"
      - "C:/Windows/System32/wbem/git.com"
      - "C:/Windows/System32/wbem/git.exe"
      - "C:/Windows/System32/wbem/git"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/git"
      - "C:/Windows/System32/OpenSSH/git.com"
      - "C:/Windows/System32/OpenSSH/git.exe"
      - "C:/Windows/System32/OpenSSH/git"
      - "C:/Users/<USER>/AppData/Local/nvm/git.com"
      - "C:/Users/<USER>/AppData/Local/nvm/git.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/git"
      - "C:/nvm4w/nodejs/git.com"
      - "C:/nvm4w/nodejs/git.exe"
      - "C:/nvm4w/nodejs/git"
      - "C:/Program Files/Go/bin/git.com"
      - "C:/Program Files/Go/bin/git.exe"
      - "C:/Program Files/Go/bin/git"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/git"
      - "C:/Program Files/Git/cmd/git.com"
    found: "C:/Program Files/Git/cmd/git.exe"
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:188 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "MEMORYCHECK_COMMAND"
    description: "Path to the memory checking command, used for memory error detection."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "purify"
      - "valgrind"
      - "boundscheck"
      - "drmemory"
      - "cuda-memcheck"
      - "compute-sanitizer"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
      - "/REGISTRY-NOTFOUND/"
    searched_directories:
      - "C:/Windows/System32/purify.com"
      - "C:/Windows/System32/purify.exe"
      - "C:/Windows/System32/purify"
      - "C:/Windows/purify.com"
      - "C:/Windows/purify.exe"
      - "C:/Windows/purify"
      - "C:/Windows/System32/wbem/purify.com"
      - "C:/Windows/System32/wbem/purify.exe"
      - "C:/Windows/System32/wbem/purify"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify"
      - "C:/Windows/System32/OpenSSH/purify.com"
      - "C:/Windows/System32/OpenSSH/purify.exe"
      - "C:/Windows/System32/OpenSSH/purify"
      - "C:/Users/<USER>/AppData/Local/nvm/purify.com"
      - "C:/Users/<USER>/AppData/Local/nvm/purify.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/purify"
      - "C:/nvm4w/nodejs/purify.com"
      - "C:/nvm4w/nodejs/purify.exe"
      - "C:/nvm4w/nodejs/purify"
      - "C:/Program Files/Go/bin/purify.com"
      - "C:/Program Files/Go/bin/purify.exe"
      - "C:/Program Files/Go/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify"
      - "C:/Program Files/Git/cmd/purify.com"
      - "C:/Program Files/Git/cmd/purify.exe"
      - "C:/Program Files/Git/cmd/purify"
      - "C:/Program Files/Void/bin/purify.com"
      - "C:/Program Files/Void/bin/purify.exe"
      - "C:/Program Files/Void/bin/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Users/<USER>/.cargo/bin/purify.com"
      - "C:/Users/<USER>/.cargo/bin/purify.exe"
      - "C:/Users/<USER>/.cargo/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/purify"
      - "C:/Users/<USER>/go/bin/purify.com"
      - "C:/Users/<USER>/go/bin/purify.exe"
      - "C:/Users/<USER>/go/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/purify"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/purify.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/purify.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/purify"
      - "C:/Program Files/bin/purify.com"
      - "C:/Program Files/bin/purify.exe"
      - "C:/Program Files/bin/purify"
      - "C:/Program Files/sbin/purify.com"
      - "C:/Program Files/sbin/purify.exe"
      - "C:/Program Files/sbin/purify"
      - "C:/Program Files/purify.com"
      - "C:/Program Files/purify.exe"
      - "C:/Program Files/purify"
      - "C:/Program Files (x86)/bin/purify.com"
      - "C:/Program Files (x86)/bin/purify.exe"
      - "C:/Program Files (x86)/bin/purify"
      - "C:/Program Files (x86)/sbin/purify.com"
      - "C:/Program Files (x86)/sbin/purify.exe"
      - "C:/Program Files (x86)/sbin/purify"
      - "C:/Program Files (x86)/purify.com"
      - "C:/Program Files (x86)/purify.exe"
      - "C:/Program Files (x86)/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Program Files/CMake/sbin/purify.com"
      - "C:/Program Files/CMake/sbin/purify.exe"
      - "C:/Program Files/CMake/sbin/purify"
      - "C:/Program Files/CMake/purify.com"
      - "C:/Program Files/CMake/purify.exe"
      - "C:/Program Files/CMake/purify"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/purify"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/purify"
      - "C:/Program Files (x86)/whisper.cpp/purify.com"
      - "C:/Program Files (x86)/whisper.cpp/purify.exe"
      - "C:/Program Files (x86)/whisper.cpp/purify"
      - "/REGISTRY-NOTFOUND/purify.com"
      - "/REGISTRY-NOTFOUND/purify.exe"
      - "/REGISTRY-NOTFOUND/purify"
      - "C:/Windows/System32/valgrind.com"
      - "C:/Windows/System32/valgrind.exe"
      - "C:/Windows/System32/valgrind"
      - "C:/Windows/valgrind.com"
      - "C:/Windows/valgrind.exe"
      - "C:/Windows/valgrind"
      - "C:/Windows/System32/wbem/valgrind.com"
      - "C:/Windows/System32/wbem/valgrind.exe"
      - "C:/Windows/System32/wbem/valgrind"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind"
      - "C:/Windows/System32/OpenSSH/valgrind.com"
      - "C:/Windows/System32/OpenSSH/valgrind.exe"
      - "C:/Windows/System32/OpenSSH/valgrind"
      - "C:/Users/<USER>/AppData/Local/nvm/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/nvm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/valgrind"
      - "C:/nvm4w/nodejs/valgrind.com"
      - "C:/nvm4w/nodejs/valgrind.exe"
      - "C:/nvm4w/nodejs/valgrind"
      - "C:/Program Files/Go/bin/valgrind.com"
      - "C:/Program Files/Go/bin/valgrind.exe"
      - "C:/Program Files/Go/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind"
      - "C:/Program Files/Git/cmd/valgrind.com"
      - "C:/Program Files/Git/cmd/valgrind.exe"
      - "C:/Program Files/Git/cmd/valgrind"
      - "C:/Program Files/Void/bin/valgrind.com"
      - "C:/Program Files/Void/bin/valgrind.exe"
      - "C:/Program Files/Void/bin/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Users/<USER>/.cargo/bin/valgrind.com"
      - "C:/Users/<USER>/.cargo/bin/valgrind.exe"
      - "C:/Users/<USER>/.cargo/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/valgrind"
      - "C:/Users/<USER>/go/bin/valgrind.com"
      - "C:/Users/<USER>/go/bin/valgrind.exe"
      - "C:/Users/<USER>/go/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/valgrind"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/valgrind.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/valgrind.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/valgrind"
      - "C:/Program Files/bin/valgrind.com"
      - "C:/Program Files/bin/valgrind.exe"
      - "C:/Program Files/bin/valgrind"
      - "C:/Program Files/sbin/valgrind.com"
      - "C:/Program Files/sbin/valgrind.exe"
      - "C:/Program Files/sbin/valgrind"
      - "C:/Program Files/valgrind.com"
      - "C:/Program Files/valgrind.exe"
      - "C:/Program Files/valgrind"
      - "C:/Program Files (x86)/bin/valgrind.com"
      - "C:/Program Files (x86)/bin/valgrind.exe"
      - "C:/Program Files (x86)/bin/valgrind"
      - "C:/Program Files (x86)/sbin/valgrind.com"
      - "C:/Program Files (x86)/sbin/valgrind.exe"
      - "C:/Program Files (x86)/sbin/valgrind"
      - "C:/Program Files (x86)/valgrind.com"
      - "C:/Program Files (x86)/valgrind.exe"
      - "C:/Program Files (x86)/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Program Files/CMake/sbin/valgrind.com"
      - "C:/Program Files/CMake/sbin/valgrind.exe"
      - "C:/Program Files/CMake/sbin/valgrind"
      - "C:/Program Files/CMake/valgrind.com"
      - "C:/Program Files/CMake/valgrind.exe"
      - "C:/Program Files/CMake/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/valgrind"
      - "C:/Program Files (x86)/whisper.cpp/valgrind.com"
      - "C:/Program Files (x86)/whisper.cpp/valgrind.exe"
      - "C:/Program Files (x86)/whisper.cpp/valgrind"
      - "/REGISTRY-NOTFOUND/valgrind.com"
      - "/REGISTRY-NOTFOUND/valgrind.exe"
      - "/REGISTRY-NOTFOUND/valgrind"
      - "C:/Windows/System32/boundscheck.com"
      - "C:/Windows/System32/boundscheck.exe"
      - "C:/Windows/System32/boundscheck"
      - "C:/Windows/boundscheck.com"
      - "C:/Windows/boundscheck.exe"
      - "C:/Windows/boundscheck"
      - "C:/Windows/System32/wbem/boundscheck.com"
      - "C:/Windows/System32/wbem/boundscheck.exe"
      - "C:/Windows/System32/wbem/boundscheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck"
      - "C:/Windows/System32/OpenSSH/boundscheck.com"
      - "C:/Windows/System32/OpenSSH/boundscheck.exe"
      - "C:/Windows/System32/OpenSSH/boundscheck"
      - "C:/Users/<USER>/AppData/Local/nvm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/nvm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/boundscheck"
      - "C:/nvm4w/nodejs/boundscheck.com"
      - "C:/nvm4w/nodejs/boundscheck.exe"
      - "C:/nvm4w/nodejs/boundscheck"
      - "C:/Program Files/Go/bin/boundscheck.com"
      - "C:/Program Files/Go/bin/boundscheck.exe"
      - "C:/Program Files/Go/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck"
      - "C:/Program Files/Git/cmd/boundscheck.com"
      - "C:/Program Files/Git/cmd/boundscheck.exe"
      - "C:/Program Files/Git/cmd/boundscheck"
      - "C:/Program Files/Void/bin/boundscheck.com"
      - "C:/Program Files/Void/bin/boundscheck.exe"
      - "C:/Program Files/Void/bin/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.com"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.exe"
      - "C:/Users/<USER>/.cargo/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/boundscheck"
      - "C:/Users/<USER>/go/bin/boundscheck.com"
      - "C:/Users/<USER>/go/bin/boundscheck.exe"
      - "C:/Users/<USER>/go/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/boundscheck.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/boundscheck"
      - "C:/Program Files/bin/boundscheck.com"
      - "C:/Program Files/bin/boundscheck.exe"
      - "C:/Program Files/bin/boundscheck"
      - "C:/Program Files/sbin/boundscheck.com"
      - "C:/Program Files/sbin/boundscheck.exe"
      - "C:/Program Files/sbin/boundscheck"
      - "C:/Program Files/boundscheck.com"
      - "C:/Program Files/boundscheck.exe"
      - "C:/Program Files/boundscheck"
      - "C:/Program Files (x86)/bin/boundscheck.com"
      - "C:/Program Files (x86)/bin/boundscheck.exe"
      - "C:/Program Files (x86)/bin/boundscheck"
      - "C:/Program Files (x86)/sbin/boundscheck.com"
      - "C:/Program Files (x86)/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/sbin/boundscheck"
      - "C:/Program Files (x86)/boundscheck.com"
      - "C:/Program Files (x86)/boundscheck.exe"
      - "C:/Program Files (x86)/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Program Files/CMake/sbin/boundscheck.com"
      - "C:/Program Files/CMake/sbin/boundscheck.exe"
      - "C:/Program Files/CMake/sbin/boundscheck"
      - "C:/Program Files/CMake/boundscheck.com"
      - "C:/Program Files/CMake/boundscheck.exe"
      - "C:/Program Files/CMake/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/boundscheck"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck.com"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/boundscheck"
      - "/REGISTRY-NOTFOUND/boundscheck.com"
      - "/REGISTRY-NOTFOUND/boundscheck.exe"
      - "/REGISTRY-NOTFOUND/boundscheck"
      - "C:/Windows/System32/drmemory.com"
      - "C:/Windows/System32/drmemory.exe"
      - "C:/Windows/System32/drmemory"
      - "C:/Windows/drmemory.com"
      - "C:/Windows/drmemory.exe"
      - "C:/Windows/drmemory"
      - "C:/Windows/System32/wbem/drmemory.com"
      - "C:/Windows/System32/wbem/drmemory.exe"
      - "C:/Windows/System32/wbem/drmemory"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory"
      - "C:/Windows/System32/OpenSSH/drmemory.com"
      - "C:/Windows/System32/OpenSSH/drmemory.exe"
      - "C:/Windows/System32/OpenSSH/drmemory"
      - "C:/Users/<USER>/AppData/Local/nvm/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/nvm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/drmemory"
      - "C:/nvm4w/nodejs/drmemory.com"
      - "C:/nvm4w/nodejs/drmemory.exe"
      - "C:/nvm4w/nodejs/drmemory"
      - "C:/Program Files/Go/bin/drmemory.com"
      - "C:/Program Files/Go/bin/drmemory.exe"
      - "C:/Program Files/Go/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory"
      - "C:/Program Files/Git/cmd/drmemory.com"
      - "C:/Program Files/Git/cmd/drmemory.exe"
      - "C:/Program Files/Git/cmd/drmemory"
      - "C:/Program Files/Void/bin/drmemory.com"
      - "C:/Program Files/Void/bin/drmemory.exe"
      - "C:/Program Files/Void/bin/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Users/<USER>/.cargo/bin/drmemory.com"
      - "C:/Users/<USER>/.cargo/bin/drmemory.exe"
      - "C:/Users/<USER>/.cargo/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/drmemory"
      - "C:/Users/<USER>/go/bin/drmemory.com"
      - "C:/Users/<USER>/go/bin/drmemory.exe"
      - "C:/Users/<USER>/go/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/drmemory"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/drmemory.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/drmemory.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/drmemory"
      - "C:/Program Files/bin/drmemory.com"
      - "C:/Program Files/bin/drmemory.exe"
      - "C:/Program Files/bin/drmemory"
      - "C:/Program Files/sbin/drmemory.com"
      - "C:/Program Files/sbin/drmemory.exe"
      - "C:/Program Files/sbin/drmemory"
      - "C:/Program Files/drmemory.com"
      - "C:/Program Files/drmemory.exe"
      - "C:/Program Files/drmemory"
      - "C:/Program Files (x86)/bin/drmemory.com"
      - "C:/Program Files (x86)/bin/drmemory.exe"
      - "C:/Program Files (x86)/bin/drmemory"
      - "C:/Program Files (x86)/sbin/drmemory.com"
      - "C:/Program Files (x86)/sbin/drmemory.exe"
      - "C:/Program Files (x86)/sbin/drmemory"
      - "C:/Program Files (x86)/drmemory.com"
      - "C:/Program Files (x86)/drmemory.exe"
      - "C:/Program Files (x86)/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Program Files/CMake/sbin/drmemory.com"
      - "C:/Program Files/CMake/sbin/drmemory.exe"
      - "C:/Program Files/CMake/sbin/drmemory"
      - "C:/Program Files/CMake/drmemory.com"
      - "C:/Program Files/CMake/drmemory.exe"
      - "C:/Program Files/CMake/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/drmemory"
      - "C:/Program Files (x86)/whisper.cpp/drmemory.com"
      - "C:/Program Files (x86)/whisper.cpp/drmemory.exe"
      - "C:/Program Files (x86)/whisper.cpp/drmemory"
      - "/REGISTRY-NOTFOUND/drmemory.com"
      - "/REGISTRY-NOTFOUND/drmemory.exe"
      - "/REGISTRY-NOTFOUND/drmemory"
      - "C:/Windows/System32/cuda-memcheck.com"
      - "C:/Windows/System32/cuda-memcheck.exe"
      - "C:/Windows/System32/cuda-memcheck"
      - "C:/Windows/cuda-memcheck.com"
      - "C:/Windows/cuda-memcheck.exe"
      - "C:/Windows/cuda-memcheck"
      - "C:/Windows/System32/wbem/cuda-memcheck.com"
      - "C:/Windows/System32/wbem/cuda-memcheck.exe"
      - "C:/Windows/System32/wbem/cuda-memcheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.com"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.exe"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/nvm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/nvm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/cuda-memcheck"
      - "C:/nvm4w/nodejs/cuda-memcheck.com"
      - "C:/nvm4w/nodejs/cuda-memcheck.exe"
      - "C:/nvm4w/nodejs/cuda-memcheck"
      - "C:/Program Files/Go/bin/cuda-memcheck.com"
      - "C:/Program Files/Go/bin/cuda-memcheck.exe"
      - "C:/Program Files/Go/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck"
      - "C:/Program Files/Git/cmd/cuda-memcheck.com"
      - "C:/Program Files/Git/cmd/cuda-memcheck.exe"
      - "C:/Program Files/Git/cmd/cuda-memcheck"
      - "C:/Program Files/Void/bin/cuda-memcheck.com"
      - "C:/Program Files/Void/bin/cuda-memcheck.exe"
      - "C:/Program Files/Void/bin/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cuda-memcheck"
      - "C:/Users/<USER>/go/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/go/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/go/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/cuda-memcheck"
      - "C:/Program Files/bin/cuda-memcheck.com"
      - "C:/Program Files/bin/cuda-memcheck.exe"
      - "C:/Program Files/bin/cuda-memcheck"
      - "C:/Program Files/sbin/cuda-memcheck.com"
      - "C:/Program Files/sbin/cuda-memcheck.exe"
      - "C:/Program Files/sbin/cuda-memcheck"
      - "C:/Program Files/cuda-memcheck.com"
      - "C:/Program Files/cuda-memcheck.exe"
      - "C:/Program Files/cuda-memcheck"
      - "C:/Program Files (x86)/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/bin/cuda-memcheck"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/cuda-memcheck.com"
      - "C:/Program Files (x86)/cuda-memcheck.exe"
      - "C:/Program Files (x86)/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.com"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/sbin/cuda-memcheck"
      - "C:/Program Files/CMake/cuda-memcheck.com"
      - "C:/Program Files/CMake/cuda-memcheck.exe"
      - "C:/Program Files/CMake/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck.com"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck.exe"
      - "C:/Program Files (x86)/whisper.cpp/cuda-memcheck"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.com"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.exe"
      - "/REGISTRY-NOTFOUND/cuda-memcheck"
      - "C:/Windows/System32/compute-sanitizer.com"
      - "C:/Windows/System32/compute-sanitizer.exe"
      - "C:/Windows/System32/compute-sanitizer"
      - "C:/Windows/compute-sanitizer.com"
      - "C:/Windows/compute-sanitizer.exe"
      - "C:/Windows/compute-sanitizer"
      - "C:/Windows/System32/wbem/compute-sanitizer.com"
      - "C:/Windows/System32/wbem/compute-sanitizer.exe"
      - "C:/Windows/System32/wbem/compute-sanitizer"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.com"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.exe"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/nvm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/nvm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/compute-sanitizer"
      - "C:/nvm4w/nodejs/compute-sanitizer.com"
      - "C:/nvm4w/nodejs/compute-sanitizer.exe"
      - "C:/nvm4w/nodejs/compute-sanitizer"
      - "C:/Program Files/Go/bin/compute-sanitizer.com"
      - "C:/Program Files/Go/bin/compute-sanitizer.exe"
      - "C:/Program Files/Go/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer"
      - "C:/Program Files/Git/cmd/compute-sanitizer.com"
      - "C:/Program Files/Git/cmd/compute-sanitizer.exe"
      - "C:/Program Files/Git/cmd/compute-sanitizer"
      - "C:/Program Files/Void/bin/compute-sanitizer.com"
      - "C:/Program Files/Void/bin/compute-sanitizer.exe"
      - "C:/Program Files/Void/bin/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/compute-sanitizer"
      - "C:/Users/<USER>/go/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/go/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/go/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/compute-sanitizer"
      - "C:/Program Files/bin/compute-sanitizer.com"
      - "C:/Program Files/bin/compute-sanitizer.exe"
      - "C:/Program Files/bin/compute-sanitizer"
      - "C:/Program Files/sbin/compute-sanitizer.com"
      - "C:/Program Files/sbin/compute-sanitizer.exe"
      - "C:/Program Files/sbin/compute-sanitizer"
      - "C:/Program Files/compute-sanitizer.com"
      - "C:/Program Files/compute-sanitizer.exe"
      - "C:/Program Files/compute-sanitizer"
      - "C:/Program Files (x86)/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/bin/compute-sanitizer"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/compute-sanitizer.com"
      - "C:/Program Files (x86)/compute-sanitizer.exe"
      - "C:/Program Files (x86)/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.com"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/sbin/compute-sanitizer"
      - "C:/Program Files/CMake/compute-sanitizer.com"
      - "C:/Program Files/CMake/compute-sanitizer.exe"
      - "C:/Program Files/CMake/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer.com"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer.exe"
      - "C:/Program Files (x86)/whisper.cpp/compute-sanitizer"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.com"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.exe"
      - "/REGISTRY-NOTFOUND/compute-sanitizer"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:196 (find_program)"
      - "CMakeLists.txt:215 (include)"
    mode: "program"
    variable: "COVERAGE_COMMAND"
    description: "Path to the coverage program that CTest uses for performing coverage inspection"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcov"
    candidate_directories:
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Users/<USER>/AppData/Local/nvm/"
      - "C:/nvm4w/nodejs/"
      - "C:/Program Files/Go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Void/bin/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/go/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/whisper.cpp/bin/"
      - "C:/Program Files (x86)/whisper.cpp/sbin/"
      - "C:/Program Files (x86)/whisper.cpp/"
    searched_directories:
      - "C:/Windows/System32/gcov.com"
      - "C:/Windows/System32/gcov.exe"
      - "C:/Windows/System32/gcov"
      - "C:/Windows/gcov.com"
      - "C:/Windows/gcov.exe"
      - "C:/Windows/gcov"
      - "C:/Windows/System32/wbem/gcov.com"
      - "C:/Windows/System32/wbem/gcov.exe"
      - "C:/Windows/System32/wbem/gcov"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov"
      - "C:/Windows/System32/OpenSSH/gcov.com"
      - "C:/Windows/System32/OpenSSH/gcov.exe"
      - "C:/Windows/System32/OpenSSH/gcov"
      - "C:/Users/<USER>/AppData/Local/nvm/gcov.com"
      - "C:/Users/<USER>/AppData/Local/nvm/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/nvm/gcov"
      - "C:/nvm4w/nodejs/gcov.com"
      - "C:/nvm4w/nodejs/gcov.exe"
      - "C:/nvm4w/nodejs/gcov"
      - "C:/Program Files/Go/bin/gcov.com"
      - "C:/Program Files/Go/bin/gcov.exe"
      - "C:/Program Files/Go/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov"
      - "C:/Program Files/Git/cmd/gcov.com"
      - "C:/Program Files/Git/cmd/gcov.exe"
      - "C:/Program Files/Git/cmd/gcov"
      - "C:/Program Files/Void/bin/gcov.com"
      - "C:/Program Files/Void/bin/gcov.exe"
      - "C:/Program Files/Void/bin/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Users/<USER>/.cargo/bin/gcov.com"
      - "C:/Users/<USER>/.cargo/bin/gcov.exe"
      - "C:/Users/<USER>/.cargo/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcov"
      - "C:/Users/<USER>/go/bin/gcov.com"
      - "C:/Users/<USER>/go/bin/gcov.exe"
      - "C:/Users/<USER>/go/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Kiro/bin/gcov"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/gcov.com"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/gcov.exe"
      - "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/gcov"
      - "C:/Program Files/bin/gcov.com"
      - "C:/Program Files/bin/gcov.exe"
      - "C:/Program Files/bin/gcov"
      - "C:/Program Files/sbin/gcov.com"
      - "C:/Program Files/sbin/gcov.exe"
      - "C:/Program Files/sbin/gcov"
      - "C:/Program Files/gcov.com"
      - "C:/Program Files/gcov.exe"
      - "C:/Program Files/gcov"
      - "C:/Program Files (x86)/bin/gcov.com"
      - "C:/Program Files (x86)/bin/gcov.exe"
      - "C:/Program Files (x86)/bin/gcov"
      - "C:/Program Files (x86)/sbin/gcov.com"
      - "C:/Program Files (x86)/sbin/gcov.exe"
      - "C:/Program Files (x86)/sbin/gcov"
      - "C:/Program Files (x86)/gcov.com"
      - "C:/Program Files (x86)/gcov.exe"
      - "C:/Program Files (x86)/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Program Files/CMake/sbin/gcov.com"
      - "C:/Program Files/CMake/sbin/gcov.exe"
      - "C:/Program Files/CMake/sbin/gcov"
      - "C:/Program Files/CMake/gcov.com"
      - "C:/Program Files/CMake/gcov.exe"
      - "C:/Program Files/CMake/gcov"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/bin/gcov"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/sbin/gcov"
      - "C:/Program Files (x86)/whisper.cpp/gcov.com"
      - "C:/Program Files (x86)/whisper.cpp/gcov.exe"
      - "C:/Program Files (x86)/whisper.cpp/gcov"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
...

---
events:
  -
    kind: "find_package-v1"
    backtrace:
      - "examples/CMakeLists.txt:9 (find_package)"
    name: "SDL2"
    configs:
      -
        filename: "SDL2Config.cmake"
        kind: "cmake"
      -
        filename: "sdl2-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: false
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "SDL2"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: true
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/pkgRedirects/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/build/CMakeFiles/pkgRedirects/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/wbem/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/wbem/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/WindowsPowerShell/v1.0/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/WindowsPowerShell/v1.0/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/OpenSSH/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Windows/System32/OpenSSH/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/nvm/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/nvm/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/nvm4w/nodejs/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/nvm4w/nodejs/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Go/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Go/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Git/cmd/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Git/cmd/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Void/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/Void/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/CMake/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/CMake/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/.cargo/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/.cargo/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Kiro/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Local/Programs/Kiro/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/debugCommand/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/CMake/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files/CMake/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files (x86)/SDL2Config.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "C:/Program Files (x86)/sdl2-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Program Files\\Go\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Void\\bin"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\nvm"
        - "C:\\nvm4w\\nodejs"
        - "C:\\Users\\<USER>\\go\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin"
        - "c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/whisper.cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/whisper.cpp"
      CMAKE_MODULE_PATH:
        - "C:/Users/<USER>/OneDrive/Desktop/Voice-agent/whisper.cpp/cmake/"
...
