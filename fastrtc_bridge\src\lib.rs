use serde::{Deserialize, Serialize};
use tokio::sync::mpsc::{self, Receiver};
use anyhow::{Result, anyhow};
use std::sync::Arc;
use webrtc::api::APIBuilder;
use webrtc::peer_connection::configuration::RTCConfiguration;
use webrtc::peer_connection::RTCPeerConnection;
use webrtc::data_channel::{RTCDataChannel, data_channel_message::DataChannelMessage};
use webrtc::data_channel::data_channel_init::RTCDataChannelInit;
use webrtc::ice_transport::ice_server::RTCIceServer;
use webrtc::peer_connection::sdp::session_description::RTCSessionDescription;
use futures::FutureExt;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FastRTCMessage {
    pub r#type: String, // e.g. "send_input", "fetch_output", "error", "log"
    pub data: String,
}

pub struct FastRTCBridge {
    peer_connection: Arc<RTCPeerConnection>,
    data_channel: Arc<RTCDataChannel>,
    receiver: Receiver<FastRTCMessage>,
}

impl FastRTCBridge {
    /// Connect and create a data channel. You must handle SDP exchange externally (manual or via signaling server).
    pub async fn connect(label: &str) -> Result<Self> {
        let api = APIBuilder::new().build();
        let config = RTCConfiguration {
            ice_servers: vec![RTCIceServer {
                urls: vec!["stun:stun.l.google.com:19302".to_string()],
                ..Default::default()
            }],
            ..Default::default()
        };
        let peer_connection = Arc::new(api.new_peer_connection(config).await?);
        let dc_init = RTCDataChannelInit {
            ..Default::default()
        };
        let data_channel = peer_connection.create_data_channel(label, Some(dc_init)).await?;
        let (tx, rx) = mpsc::channel(32);

        // Register on_message callback for text messages
        let tx_clone = tx.clone();
        data_channel.on_message(Box::new(move |msg: DataChannelMessage| {
            let tx_inner = tx_clone.clone();
            async move {
                if msg.is_string {
                    let text = String::from_utf8_lossy(&msg.data).to_string();
                    if let Ok(parsed) = serde_json::from_str::<FastRTCMessage>(&text) {
                        let _ = tx_inner.send(parsed).await;
                    }
                }
            }.boxed()
        }));

        Ok(Self {
            peer_connection,
            data_channel,
            receiver: rx,
        })
    }

    /// Send a FastRTCMessage as JSON over the data channel
    pub async fn send_text(&self, msg: FastRTCMessage) -> Result<()> {
        let json = serde_json::to_string(&msg)?;
        // send_text returns Result<usize, Error>
        self.data_channel.send_text(json).await.map(|_| ()).map_err(|e| anyhow!(e))
    }

    /// Receive a FastRTCMessage from the data channel
    pub async fn receive_text(&mut self) -> Option<FastRTCMessage> {
        self.receiver.recv().await
    }

    /// Start the async loop (example: poll for messages and route them)
    pub async fn start(&mut self) -> Result<()> {
        while let Some(_msg) = self.receive_text().await {
            // Route to appropriate module (Whisper, Gemini, Piper)
            // e.g. match msg.r#type.as_str() { ... }
        }
        Ok(())
    }

    /// Get the local SDP offer (to send to remote peer)
    pub async fn create_offer(&self) -> Result<String> {
        let offer = self.peer_connection.create_offer(None).await?;
        self.peer_connection.set_local_description(offer.clone()).await?;
        Ok(serde_json::to_string(&offer)?)
    }

    /// Set the remote SDP answer (from remote peer)
    pub async fn set_remote_answer(&self, answer_json: &str) -> Result<()> {
        let answer: RTCSessionDescription = serde_json::from_str(answer_json)?;
        self.peer_connection.set_remote_description(answer).await?;
        Ok(())
    }
}

// Integration points:
// - Whisper sends recognized text via send_text({type: "send_input", data: ...})
// - Gemini receives text, processes, and sends reply via send_text({type: "fetch_output", data: ...})
// - Piper receives reply text and speaks it
//
// SDP exchange must be handled externally (manual copy-paste or via signaling server).
// Extend for audio channel support in the future.
