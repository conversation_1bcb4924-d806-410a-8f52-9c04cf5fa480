﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\talk-llama.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-adapter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-arch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-batch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-chat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-context.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-cparams.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-grammar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-graph.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-hparams.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-impl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-io.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-kv-cache-unified.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-kv-cache-unified-iswa.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-memory-recurrent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-memory-hybrid.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-memory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-mmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-model-loader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-model-saver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-quant.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-sampling.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\llama-vocab.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\unicode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\unicode-data.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\Voice-agent\whisper.cpp\examples\talk-llama\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{CDC56093-E337-36CB-BEF2-4F74AFE8CC5E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
